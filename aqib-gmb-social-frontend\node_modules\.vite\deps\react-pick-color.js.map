{"version": 3, "sources": ["../../react-pick-color/build/index.es.js"], "sourcesContent": ["import e,{useState as r,useRef as t,useCallback as n,useLayoutEffect as a,useEffect as o}from\"react\";var i=function(){return(i=Object.assign||function(e){for(var r,t=1,n=arguments.length;t<n;t++)for(var a in r=arguments[t])Object.prototype.hasOwnProperty.call(r,a)&&(e[a]=r[a]);return e}).apply(this,arguments)};function l(e,r,t){if(t||2===arguments.length)for(var n,a=0,o=r.length;a<o;a++)!n&&a in r||(n||(n=Array.prototype.slice.call(r,0,a)),n[a]=r[a]);return e.concat(n||Array.prototype.slice.call(r))}\"function\"==typeof SuppressedError&&SuppressedError;var s,c=(function(e){!function(r){var t=/^\\s+/,n=/\\s+$/,a=0,o=r.round,i=r.min,l=r.max,s=r.random;function c(e,s){if(s=s||{},(e=e||\"\")instanceof c)return e;if(!(this instanceof c))return new c(e,s);var u=function(e){var a,o,s,c={r:0,g:0,b:0},u=1,f=null,h=null,d=null,g=!1,p=!1;return\"string\"==typeof e&&(e=function(e){e=e.replace(t,\"\").replace(n,\"\").toLowerCase();var r,a=!1;if(R[e])e=R[e],a=!0;else if(\"transparent\"==e)return{r:0,g:0,b:0,a:0,format:\"name\"};return(r=P.rgb.exec(e))?{r:r[1],g:r[2],b:r[3]}:(r=P.rgba.exec(e))?{r:r[1],g:r[2],b:r[3],a:r[4]}:(r=P.hsl.exec(e))?{h:r[1],s:r[2],l:r[3]}:(r=P.hsla.exec(e))?{h:r[1],s:r[2],l:r[3],a:r[4]}:(r=P.hsv.exec(e))?{h:r[1],s:r[2],v:r[3]}:(r=P.hsva.exec(e))?{h:r[1],s:r[2],v:r[3],a:r[4]}:(r=P.hex8.exec(e))?{r:B(r[1]),g:B(r[2]),b:B(r[3]),a:O(r[4]),format:a?\"name\":\"hex8\"}:(r=P.hex6.exec(e))?{r:B(r[1]),g:B(r[2]),b:B(r[3]),format:a?\"name\":\"hex\"}:(r=P.hex4.exec(e))?{r:B(r[1]+\"\"+r[1]),g:B(r[2]+\"\"+r[2]),b:B(r[3]+\"\"+r[3]),a:O(r[4]+\"\"+r[4]),format:a?\"name\":\"hex8\"}:!!(r=P.hex3.exec(e))&&{r:B(r[1]+\"\"+r[1]),g:B(r[2]+\"\"+r[2]),b:B(r[3]+\"\"+r[3]),format:a?\"name\":\"hex\"}}(e)),\"object\"==typeof e&&(j(e.r)&&j(e.g)&&j(e.b)?(a=e.r,o=e.g,s=e.b,c={r:255*F(a,255),g:255*F(o,255),b:255*F(s,255)},g=!0,p=\"%\"===String(e.r).substr(-1)?\"prgb\":\"rgb\"):j(e.h)&&j(e.s)&&j(e.v)?(f=N(e.s),h=N(e.v),c=function(e,t,n){e=6*F(e,360),t=F(t,100),n=F(n,100);var a=r.floor(e),o=e-a,i=n*(1-t),l=n*(1-o*t),s=n*(1-(1-o)*t),c=a%6;return{r:255*[n,l,i,i,s,n][c],g:255*[s,n,n,l,i,i][c],b:255*[i,i,s,n,n,l][c]}}(e.h,f,h),g=!0,p=\"hsv\"):j(e.h)&&j(e.s)&&j(e.l)&&(f=N(e.s),d=N(e.l),c=function(e,r,t){var n,a,o;function i(e,r,t){return t<0&&(t+=1),t>1&&(t-=1),t<1/6?e+6*(r-e)*t:t<.5?r:t<2/3?e+(r-e)*(2/3-t)*6:e}if(e=F(e,360),r=F(r,100),t=F(t,100),0===r)n=a=o=t;else{var l=t<.5?t*(1+r):t+r-t*r,s=2*t-l;n=i(s,l,e+1/3),a=i(s,l,e),o=i(s,l,e-1/3)}return{r:255*n,g:255*a,b:255*o}}(e.h,f,d),g=!0,p=\"hsl\"),e.hasOwnProperty(\"a\")&&(u=e.a)),u=H(u),{ok:g,format:e.format||p,r:i(255,l(c.r,0)),g:i(255,l(c.g,0)),b:i(255,l(c.b,0)),a:u}}(e);this._originalInput=e,this._r=u.r,this._g=u.g,this._b=u.b,this._a=u.a,this._roundA=o(100*this._a)/100,this._format=s.format||u.format,this._gradientType=s.gradientType,this._r<1&&(this._r=o(this._r)),this._g<1&&(this._g=o(this._g)),this._b<1&&(this._b=o(this._b)),this._ok=u.ok,this._tc_id=a++}function u(e,r,t){e=F(e,255),r=F(r,255),t=F(t,255);var n,a,o=l(e,r,t),s=i(e,r,t),c=(o+s)/2;if(o==s)n=a=0;else{var u=o-s;switch(a=c>.5?u/(2-o-s):u/(o+s),o){case e:n=(r-t)/u+(r<t?6:0);break;case r:n=(t-e)/u+2;break;case t:n=(e-r)/u+4}n/=6}return{h:n,s:a,l:c}}function f(e,r,t){e=F(e,255),r=F(r,255),t=F(t,255);var n,a,o=l(e,r,t),s=i(e,r,t),c=o,u=o-s;if(a=0===o?0:u/o,o==s)n=0;else{switch(o){case e:n=(r-t)/u+(r<t?6:0);break;case r:n=(t-e)/u+2;break;case t:n=(e-r)/u+4}n/=6}return{h:n,s:a,v:c}}function h(e,r,t,n){var a=[U(o(e).toString(16)),U(o(r).toString(16)),U(o(t).toString(16))];return n&&a[0].charAt(0)==a[0].charAt(1)&&a[1].charAt(0)==a[1].charAt(1)&&a[2].charAt(0)==a[2].charAt(1)?a[0].charAt(0)+a[1].charAt(0)+a[2].charAt(0):a.join(\"\")}function d(e,r,t,n){return[U(z(n)),U(o(e).toString(16)),U(o(r).toString(16)),U(o(t).toString(16))].join(\"\")}function g(e,r){r=0===r?0:r||10;var t=c(e).toHsl();return t.s-=r/100,t.s=I(t.s),c(t)}function p(e,r){r=0===r?0:r||10;var t=c(e).toHsl();return t.s+=r/100,t.s=I(t.s),c(t)}function b(e){return c(e).desaturate(100)}function m(e,r){r=0===r?0:r||10;var t=c(e).toHsl();return t.l+=r/100,t.l=I(t.l),c(t)}function v(e,r){r=0===r?0:r||10;var t=c(e).toRgb();return t.r=l(0,i(255,t.r-o(-r/100*255))),t.g=l(0,i(255,t.g-o(-r/100*255))),t.b=l(0,i(255,t.b-o(-r/100*255))),c(t)}function x(e,r){r=0===r?0:r||10;var t=c(e).toHsl();return t.l-=r/100,t.l=I(t.l),c(t)}function A(e,r){var t=c(e).toHsl(),n=(t.h+r)%360;return t.h=n<0?360+n:n,c(t)}function y(e){var r=c(e).toHsl();return r.h=(r.h+180)%360,c(r)}function _(e){var r=c(e).toHsl(),t=r.h;return[c(e),c({h:(t+120)%360,s:r.s,l:r.l}),c({h:(t+240)%360,s:r.s,l:r.l})]}function w(e){var r=c(e).toHsl(),t=r.h;return[c(e),c({h:(t+90)%360,s:r.s,l:r.l}),c({h:(t+180)%360,s:r.s,l:r.l}),c({h:(t+270)%360,s:r.s,l:r.l})]}function k(e){var r=c(e).toHsl(),t=r.h;return[c(e),c({h:(t+72)%360,s:r.s,l:r.l}),c({h:(t+216)%360,s:r.s,l:r.l})]}function E(e,r,t){r=r||6,t=t||30;var n=c(e).toHsl(),a=360/t,o=[c(e)];for(n.h=(n.h-(a*r>>1)+720)%360;--r;)n.h=(n.h+a)%360,o.push(c(n));return o}function S(e,r){r=r||6;for(var t=c(e).toHsv(),n=t.h,a=t.s,o=t.v,i=[],l=1/r;r--;)i.push(c({h:n,s:a,v:o})),o=(o+l)%1;return i}c.prototype={isDark:function(){return this.getBrightness()<128},isLight:function(){return!this.isDark()},isValid:function(){return this._ok},getOriginalInput:function(){return this._originalInput},getFormat:function(){return this._format},getAlpha:function(){return this._a},getBrightness:function(){var e=this.toRgb();return(299*e.r+587*e.g+114*e.b)/1e3},getLuminance:function(){var e,t,n,a=this.toRgb();return e=a.r/255,t=a.g/255,n=a.b/255,.2126*(e<=.03928?e/12.92:r.pow((e+.055)/1.055,2.4))+.7152*(t<=.03928?t/12.92:r.pow((t+.055)/1.055,2.4))+.0722*(n<=.03928?n/12.92:r.pow((n+.055)/1.055,2.4))},setAlpha:function(e){return this._a=H(e),this._roundA=o(100*this._a)/100,this},toHsv:function(){var e=f(this._r,this._g,this._b);return{h:360*e.h,s:e.s,v:e.v,a:this._a}},toHsvString:function(){var e=f(this._r,this._g,this._b),r=o(360*e.h),t=o(100*e.s),n=o(100*e.v);return 1==this._a?\"hsv(\"+r+\", \"+t+\"%, \"+n+\"%)\":\"hsva(\"+r+\", \"+t+\"%, \"+n+\"%, \"+this._roundA+\")\"},toHsl:function(){var e=u(this._r,this._g,this._b);return{h:360*e.h,s:e.s,l:e.l,a:this._a}},toHslString:function(){var e=u(this._r,this._g,this._b),r=o(360*e.h),t=o(100*e.s),n=o(100*e.l);return 1==this._a?\"hsl(\"+r+\", \"+t+\"%, \"+n+\"%)\":\"hsla(\"+r+\", \"+t+\"%, \"+n+\"%, \"+this._roundA+\")\"},toHex:function(e){return h(this._r,this._g,this._b,e)},toHexString:function(e){return\"#\"+this.toHex(e)},toHex8:function(e){return function(e,r,t,n,a){var i=[U(o(e).toString(16)),U(o(r).toString(16)),U(o(t).toString(16)),U(z(n))];return a&&i[0].charAt(0)==i[0].charAt(1)&&i[1].charAt(0)==i[1].charAt(1)&&i[2].charAt(0)==i[2].charAt(1)&&i[3].charAt(0)==i[3].charAt(1)?i[0].charAt(0)+i[1].charAt(0)+i[2].charAt(0)+i[3].charAt(0):i.join(\"\")}(this._r,this._g,this._b,this._a,e)},toHex8String:function(e){return\"#\"+this.toHex8(e)},toRgb:function(){return{r:o(this._r),g:o(this._g),b:o(this._b),a:this._a}},toRgbString:function(){return 1==this._a?\"rgb(\"+o(this._r)+\", \"+o(this._g)+\", \"+o(this._b)+\")\":\"rgba(\"+o(this._r)+\", \"+o(this._g)+\", \"+o(this._b)+\", \"+this._roundA+\")\"},toPercentageRgb:function(){return{r:o(100*F(this._r,255))+\"%\",g:o(100*F(this._g,255))+\"%\",b:o(100*F(this._b,255))+\"%\",a:this._a}},toPercentageRgbString:function(){return 1==this._a?\"rgb(\"+o(100*F(this._r,255))+\"%, \"+o(100*F(this._g,255))+\"%, \"+o(100*F(this._b,255))+\"%)\":\"rgba(\"+o(100*F(this._r,255))+\"%, \"+o(100*F(this._g,255))+\"%, \"+o(100*F(this._b,255))+\"%, \"+this._roundA+\")\"},toName:function(){return 0===this._a?\"transparent\":!(this._a<1)&&(C[h(this._r,this._g,this._b,!0)]||!1)},toFilter:function(e){var r=\"#\"+d(this._r,this._g,this._b,this._a),t=r,n=this._gradientType?\"GradientType = 1, \":\"\";if(e){var a=c(e);t=\"#\"+d(a._r,a._g,a._b,a._a)}return\"progid:DXImageTransform.Microsoft.gradient(\"+n+\"startColorstr=\"+r+\",endColorstr=\"+t+\")\"},toString:function(e){var r=!!e;e=e||this._format;var t=!1,n=this._a<1&&this._a>=0;return r||!n||\"hex\"!==e&&\"hex6\"!==e&&\"hex3\"!==e&&\"hex4\"!==e&&\"hex8\"!==e&&\"name\"!==e?(\"rgb\"===e&&(t=this.toRgbString()),\"prgb\"===e&&(t=this.toPercentageRgbString()),\"hex\"!==e&&\"hex6\"!==e||(t=this.toHexString()),\"hex3\"===e&&(t=this.toHexString(!0)),\"hex4\"===e&&(t=this.toHex8String(!0)),\"hex8\"===e&&(t=this.toHex8String()),\"name\"===e&&(t=this.toName()),\"hsl\"===e&&(t=this.toHslString()),\"hsv\"===e&&(t=this.toHsvString()),t||this.toHexString()):\"name\"===e&&0===this._a?this.toName():this.toRgbString()},clone:function(){return c(this.toString())},_applyModification:function(e,r){var t=e.apply(null,[this].concat([].slice.call(r)));return this._r=t._r,this._g=t._g,this._b=t._b,this.setAlpha(t._a),this},lighten:function(){return this._applyModification(m,arguments)},brighten:function(){return this._applyModification(v,arguments)},darken:function(){return this._applyModification(x,arguments)},desaturate:function(){return this._applyModification(g,arguments)},saturate:function(){return this._applyModification(p,arguments)},greyscale:function(){return this._applyModification(b,arguments)},spin:function(){return this._applyModification(A,arguments)},_applyCombination:function(e,r){return e.apply(null,[this].concat([].slice.call(r)))},analogous:function(){return this._applyCombination(E,arguments)},complement:function(){return this._applyCombination(y,arguments)},monochromatic:function(){return this._applyCombination(S,arguments)},splitcomplement:function(){return this._applyCombination(k,arguments)},triad:function(){return this._applyCombination(_,arguments)},tetrad:function(){return this._applyCombination(w,arguments)}},c.fromRatio=function(e,r){if(\"object\"==typeof e){var t={};for(var n in e)e.hasOwnProperty(n)&&(t[n]=\"a\"===n?e[n]:N(e[n]));e=t}return c(e,r)},c.equals=function(e,r){return!(!e||!r)&&c(e).toRgbString()==c(r).toRgbString()},c.random=function(){return c.fromRatio({r:s(),g:s(),b:s()})},c.mix=function(e,r,t){t=0===t?0:t||50;var n=c(e).toRgb(),a=c(r).toRgb(),o=t/100;return c({r:(a.r-n.r)*o+n.r,g:(a.g-n.g)*o+n.g,b:(a.b-n.b)*o+n.b,a:(a.a-n.a)*o+n.a})},c.readability=function(e,t){var n=c(e),a=c(t);return(r.max(n.getLuminance(),a.getLuminance())+.05)/(r.min(n.getLuminance(),a.getLuminance())+.05)},c.isReadable=function(e,r,t){var n,a,o,i,l,s=c.readability(e,r);switch(a=!1,(o=t,i=((o=o||{level:\"AA\",size:\"small\"}).level||\"AA\").toUpperCase(),l=(o.size||\"small\").toLowerCase(),\"AA\"!==i&&\"AAA\"!==i&&(i=\"AA\"),\"small\"!==l&&\"large\"!==l&&(l=\"small\"),n={level:i,size:l}).level+n.size){case\"AAsmall\":case\"AAAlarge\":a=s>=4.5;break;case\"AAlarge\":a=s>=3;break;case\"AAAsmall\":a=s>=7}return a},c.mostReadable=function(e,r,t){var n,a,o,i,l=null,s=0;a=(t=t||{}).includeFallbackColors,o=t.level,i=t.size;for(var u=0;u<r.length;u++)(n=c.readability(e,r[u]))>s&&(s=n,l=c(r[u]));return c.isReadable(e,l,{level:o,size:i})||!a?l:(t.includeFallbackColors=!1,c.mostReadable(e,[\"#fff\",\"#000\"],t))};var R=c.names={aliceblue:\"f0f8ff\",antiquewhite:\"faebd7\",aqua:\"0ff\",aquamarine:\"7fffd4\",azure:\"f0ffff\",beige:\"f5f5dc\",bisque:\"ffe4c4\",black:\"000\",blanchedalmond:\"ffebcd\",blue:\"00f\",blueviolet:\"8a2be2\",brown:\"a52a2a\",burlywood:\"deb887\",burntsienna:\"ea7e5d\",cadetblue:\"5f9ea0\",chartreuse:\"7fff00\",chocolate:\"d2691e\",coral:\"ff7f50\",cornflowerblue:\"6495ed\",cornsilk:\"fff8dc\",crimson:\"dc143c\",cyan:\"0ff\",darkblue:\"00008b\",darkcyan:\"008b8b\",darkgoldenrod:\"b8860b\",darkgray:\"a9a9a9\",darkgreen:\"006400\",darkgrey:\"a9a9a9\",darkkhaki:\"bdb76b\",darkmagenta:\"8b008b\",darkolivegreen:\"556b2f\",darkorange:\"ff8c00\",darkorchid:\"9932cc\",darkred:\"8b0000\",darksalmon:\"e9967a\",darkseagreen:\"8fbc8f\",darkslateblue:\"483d8b\",darkslategray:\"2f4f4f\",darkslategrey:\"2f4f4f\",darkturquoise:\"00ced1\",darkviolet:\"9400d3\",deeppink:\"ff1493\",deepskyblue:\"00bfff\",dimgray:\"696969\",dimgrey:\"696969\",dodgerblue:\"1e90ff\",firebrick:\"b22222\",floralwhite:\"fffaf0\",forestgreen:\"228b22\",fuchsia:\"f0f\",gainsboro:\"dcdcdc\",ghostwhite:\"f8f8ff\",gold:\"ffd700\",goldenrod:\"daa520\",gray:\"808080\",green:\"008000\",greenyellow:\"adff2f\",grey:\"808080\",honeydew:\"f0fff0\",hotpink:\"ff69b4\",indianred:\"cd5c5c\",indigo:\"4b0082\",ivory:\"fffff0\",khaki:\"f0e68c\",lavender:\"e6e6fa\",lavenderblush:\"fff0f5\",lawngreen:\"7cfc00\",lemonchiffon:\"fffacd\",lightblue:\"add8e6\",lightcoral:\"f08080\",lightcyan:\"e0ffff\",lightgoldenrodyellow:\"fafad2\",lightgray:\"d3d3d3\",lightgreen:\"90ee90\",lightgrey:\"d3d3d3\",lightpink:\"ffb6c1\",lightsalmon:\"ffa07a\",lightseagreen:\"20b2aa\",lightskyblue:\"87cefa\",lightslategray:\"789\",lightslategrey:\"789\",lightsteelblue:\"b0c4de\",lightyellow:\"ffffe0\",lime:\"0f0\",limegreen:\"32cd32\",linen:\"faf0e6\",magenta:\"f0f\",maroon:\"800000\",mediumaquamarine:\"66cdaa\",mediumblue:\"0000cd\",mediumorchid:\"ba55d3\",mediumpurple:\"9370db\",mediumseagreen:\"3cb371\",mediumslateblue:\"7b68ee\",mediumspringgreen:\"00fa9a\",mediumturquoise:\"48d1cc\",mediumvioletred:\"c71585\",midnightblue:\"191970\",mintcream:\"f5fffa\",mistyrose:\"ffe4e1\",moccasin:\"ffe4b5\",navajowhite:\"ffdead\",navy:\"000080\",oldlace:\"fdf5e6\",olive:\"808000\",olivedrab:\"6b8e23\",orange:\"ffa500\",orangered:\"ff4500\",orchid:\"da70d6\",palegoldenrod:\"eee8aa\",palegreen:\"98fb98\",paleturquoise:\"afeeee\",palevioletred:\"db7093\",papayawhip:\"ffefd5\",peachpuff:\"ffdab9\",peru:\"cd853f\",pink:\"ffc0cb\",plum:\"dda0dd\",powderblue:\"b0e0e6\",purple:\"800080\",rebeccapurple:\"663399\",red:\"f00\",rosybrown:\"bc8f8f\",royalblue:\"4169e1\",saddlebrown:\"8b4513\",salmon:\"fa8072\",sandybrown:\"f4a460\",seagreen:\"2e8b57\",seashell:\"fff5ee\",sienna:\"a0522d\",silver:\"c0c0c0\",skyblue:\"87ceeb\",slateblue:\"6a5acd\",slategray:\"708090\",slategrey:\"708090\",snow:\"fffafa\",springgreen:\"00ff7f\",steelblue:\"4682b4\",tan:\"d2b48c\",teal:\"008080\",thistle:\"d8bfd8\",tomato:\"ff6347\",turquoise:\"40e0d0\",violet:\"ee82ee\",wheat:\"f5deb3\",white:\"fff\",whitesmoke:\"f5f5f5\",yellow:\"ff0\",yellowgreen:\"9acd32\"},C=c.hexNames=function(e){var r={};for(var t in e)e.hasOwnProperty(t)&&(r[e[t]]=t);return r}(R);function H(e){return e=parseFloat(e),(isNaN(e)||e<0||e>1)&&(e=1),e}function F(e,t){(function(e){return\"string\"==typeof e&&-1!=e.indexOf(\".\")&&1===parseFloat(e)})(e)&&(e=\"100%\");var n=function(e){return\"string\"==typeof e&&-1!=e.indexOf(\"%\")}(e);return e=i(t,l(0,parseFloat(e))),n&&(e=parseInt(e*t,10)/100),r.abs(e-t)<1e-6?1:e%t/parseFloat(t)}function I(e){return i(1,l(0,e))}function B(e){return parseInt(e,16)}function U(e){return 1==e.length?\"0\"+e:\"\"+e}function N(e){return e<=1&&(e=100*e+\"%\"),e}function z(e){return r.round(255*parseFloat(e)).toString(16)}function O(e){return B(e)/255}var M,L,T,P=(L=\"[\\\\s|\\\\(]+(\"+(M=\"(?:[-\\\\+]?\\\\d*\\\\.\\\\d+%?)|(?:[-\\\\+]?\\\\d+%?)\")+\")[,|\\\\s]+(\"+M+\")[,|\\\\s]+(\"+M+\")\\\\s*\\\\)?\",T=\"[\\\\s|\\\\(]+(\"+M+\")[,|\\\\s]+(\"+M+\")[,|\\\\s]+(\"+M+\")[,|\\\\s]+(\"+M+\")\\\\s*\\\\)?\",{CSS_UNIT:new RegExp(M),rgb:new RegExp(\"rgb\"+L),rgba:new RegExp(\"rgba\"+T),hsl:new RegExp(\"hsl\"+L),hsla:new RegExp(\"hsla\"+T),hsv:new RegExp(\"hsv\"+L),hsva:new RegExp(\"hsva\"+T),hex3:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex6:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,hex4:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex8:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/});function j(e){return!!P.CSS_UNIT.exec(e)}e.exports?e.exports=c:window.tinycolor=c}(Math)}(s={exports:{}},s.exports),s.exports),u=function(e){var r=c(e);return r.isValid()?{hex:r.toHexString(),rgb:r.toRgb(),hsl:r.toHsl(),hsv:r.toHsv(),alpha:r.getAlpha()}:{hex:\"#000000\",rgb:{r:0,g:0,b:0,a:1},hsl:{h:0,s:0,l:0,a:1},hsv:{h:0,s:0,v:0,a:1},alpha:1}},f=function(e,r){var t=e.hex,n=c(t),a=[];return(\"string\"==typeof r?[r]:r).forEach((function(e){return\"analogous\"===e?n.analogous().forEach((function(e){return a.push(e.toHexString())})):\"monochromatic\"===e?n.monochromatic().forEach((function(e){return a.push(e.toHexString())})):\"splitcomplement\"===e?n.splitcomplement().forEach((function(e){return a.push(e.toHexString())})):\"tetrad\"===e?n.tetrad().forEach((function(e){return a.push(e.toHexString())})):\"triad\"===e?n.triad().forEach((function(e){return a.push(e.toHexString())})):a.push(n.complement().toHexString())})),a},h={light:{background:\"#fff\",inputBackground:\"#f4f4f4\",color:\"#262626\",borderColor:\"#D4D4D4\",borderRadius:\"4px\",boxShadow:\"0px 8px 16px rgba(0, 0, 0, 0.1)\",width:\"280px\"},dark:{background:\"rgba(40, 40, 40, 0.95)\",inputBackground:\"#454545\",color:\"#E3E3E3\",borderRadius:\"4px\",borderColor:\"#575657\",boxShadow:\"0px 8px 16px rgba(0, 0, 0, 0.1)\",width:\"280px\"}},d={width:\"100%\",height:\"8px\",background:\"linear-gradient(to right, #f00 0%, #ff0 17%, #0f0 33%, #0ff 50%, #00f 67%, #f0f 83%, #f00 100%)\",boxShadow:\"inset 0 0 1px rgba(0, 0, 0, .2)\",position:\"relative\",borderRadius:\"4px\"},g={width:\"calc(100% - 8px)\",height:\"100%\",position:\"relative\",marginLeft:\"8px\"},p={width:\"12px\",borderRadius:\"12px\",height:\"12px\",boxSizing:\"border-box\",boxShadow:\"0 0 1px rgba(0, 0, 0, .9)\",border:\"2px solid #fff\",position:\"absolute\",transform:\"translate(-2px, -2px)\",cursor:\"pointer\",left:\"calc(var(--rpc-hue-pointer) - 8px)\"},b=function(e){return e>1?1:e<0?0:e},m=function(e){var o=e.onMove,i=r(!1),l=i[0],s=i[1],c=t(null),u=n((function(e){if(!c.current)return{left:0,top:0};var r=c.current.getBoundingClientRect(),t=r.width,n=r.left,a=r.top,o=r.height,i=e.pageX&&\"number\"==typeof e.pageX?e:e.touches[0],l=i.pageX,s=i.pageY;return{left:b((l-(n+window.pageXOffset))/t),top:b((s-(a+window.pageYOffset))/o)}}),[]),f=n((function(e){e.preventDefault(),c.current&&o(u(e))}),[o,u]),h=n((function(e){o(u(e)),s(!0)}),[o,u]),d=n((function(){return s(!1)}),[]),g=n((function(e){var r=e?document.addEventListener:document.removeEventListener;r(\"mousemove\",f),r(\"touchmove\",f),r(\"mouseup\",d),r(\"touchend\",d)}),[f,d]);return a((function(){return g(l),function(){l&&g(!1)}}),[l,g]),{ref:c,handleStart:h}},v=e.memo((function(r){var t=r.hsl,a=r.onChange,o=n((function(e){var r=e.left;return a&&a({h:360*r,s:t.s,l:t.l,a:t.a})}),[a]),i=m({onMove:o}),l=i.ref,s=i.handleStart;return e.createElement(\"div\",{style:d,ref:l,onTouchStart:s,onMouseDown:s},e.createElement(\"div\",{style:g},e.createElement(\"div\",{style:p})))})),x={margin:\"5px 0 0\",width:\"100%\",height:\"8px\",boxShadow:\"inset 0 0 1px rgba(0, 0, 0, .2)\",position:\"relative\",zIndex:1,borderRadius:\"4px\",background:\"var(--rpc-input-background)\"},A={position:\"absolute\",top:0,left:0,right:0,bottom:0,background:\"linear-gradient(to right, rgba(var(--rpc-red), var(--rpc-green), var(--rpc-blue), 0) 0%, rgba(var(--rpc-red), var(--rpc-green), var(--rpc-blue), 1) 100%)\",borderRadius:\"4px\"},y={width:\"calc(100% - 8px)\",height:\"100%\",position:\"relative\",marginLeft:\"8px\"},_={width:\"12px\",borderRadius:\"12px\",height:\"12px\",boxShadow:\"0 0 1px rgba(0, 0, 0, .9)\",border:\"2px solid #fff\",position:\"absolute\",transform:\"translate(-2px, -2px)\",cursor:\"pointer\",left:\"calc(var(--rpc-alpha-pointer) - 8px)\",boxSizing:\"border-box\"},w={position:\"absolute\",top:\"0px\",right:\"0px\",bottom:\"0px\",left:\"0px\",backgroundImage:'url(\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAC9JREFUOBFjZGBgEAFifOANPkkmfJLEyI0awMAw8GHASERU4U0nA++FURdQISEBAFeUATP+HuV8AAAAAElFTkSuQmCC\")',backgroundPosition:\"left center\",zIndex:-1,borderRadius:\"calc(var(--rpc-border-radius) / 2)\"},k=e.memo((function(r){var t=r.onChange,a=n((function(e){var r=e.left;return t&&t(parseFloat(r.toFixed(2)))}),[t]),o=m({onMove:a}),i=o.ref,l=o.handleStart;return e.createElement(\"div\",{style:x,ref:i,onTouchStart:l,onMouseDown:l},e.createElement(\"div\",{style:A}),e.createElement(\"div\",{style:w}),e.createElement(\"div\",{style:y},e.createElement(\"div\",{style:_})))})),E={padding:\"0.4rem\",borderTop:\"1px solid var(--rpc-border-color)\",display:\"grid\",gridTemplateColumns:\"repeat(10, 1fr)\",gridGap:\"0.4rem\"},S={height:\"20px\",margin:\"0\",outline:\"none\",appearance:\"none\",cursor:\"pointer\",padding:\"0\",borderRadius:\"var(--rpc-border-radius)\",border:\"1px solid var(--rpc-border-color)\",overflow:\"hidden\",position:\"relative\",background:\"none\",gridColumnEnd:\"span 1\"},R={height:\"20px\",margin:\"0\",padding:\"0\",outline:\"none\",background:\"none\",display:\"flex\",alignItems:\"center\",justifyContent:\"center\",cursor:\"pointer\",border:\"none\",borderRadius:\"var(--rpc-border-radius)\",gridColumnEnd:\"span 1\"},C={height:\"9px\",width:\"9px\",fill:\"var(--rpc-color)\"},H=function(e){return{position:\"absolute\",background:\"rgba(\".concat(e.r,\", \").concat(e.g,\", \").concat(e.b,\", \").concat(e.a,\")\"),top:\"0px\",right:\"0px\",bottom:\"0px\",left:\"0px\",zIndex:2}},F={position:\"absolute\",top:\"0px\",right:\"0px\",bottom:\"0px\",left:\"0px\",backgroundImage:'url(\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAC9JREFUOBFjZGBgEAFifOANPkkmfJLEyI0awMAw8GHASERU4U0nA++FURdQISEBAFeUATP+HuV8AAAAAElFTkSuQmCC\")',backgroundPosition:\"left center\",zIndex:1},I=e.memo((function(r){var t=r.colors,n=r.onClick,a=r.onAdd;return e.createElement(\"div\",{style:E},t.map((function(r,t){var a=c(r);if(!a.isValid())throw Error(\"\".concat(r,\" is not a valid color.\"));var o=a.toRgb();return e.createElement(\"button\",{key:t,style:S,onClick:function(){return n(o)},type:\"button\"},e.createElement(\"div\",{style:H(o)}),e.createElement(\"div\",{style:F}))})),a&&e.createElement(\"button\",{style:R,onClick:a,type:\"button\"},e.createElement(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",width:\"50\",height:\"50\",viewBox:\"0 0 50 50\",style:C},e.createElement(\"path\",{d:\"M27.5 50h-5V27.5H0v-5h22.5V0h5v22.5H50v5H27.5z\"}))))})),B={position:\"absolute\",top:0,left:0,bottom:0,right:0},U={width:\"calc(100% - 0.8rem)\",height:\"200px\",margin:\"0.4rem\",position:\"relative\",background:\"hsl(var(--rpc-hue),100%, 50%)\",borderRadius:\"var(--rpc-border-radius)\",border:\"1px solid var(--rpc-border-color)\",overflow:\"hidden\"},N={position:\"absolute\",cursor:\"hand\",top:\"var(--rpc-saturation-pointer-top)\",left:\"var(--rpc-saturation-pointer-left)\"},z={width:\"12px\",borderRadius:\"12px\",height:\"12px\",boxSizing:\"border-box\",boxShadow:\"0 0 1px rgba(0, 0, 0, .9)\",border:\"2px solid #fff\"},O=e.memo((function(r){var t=r.hsl,a=r.onChange,o=n((function(e){var r=e.left,n=e.top;return a&&a(i(i({},t),{s:r,v:1-n}))}),[a]),l=m({onMove:o}),s=l.ref,c=l.handleStart;return e.createElement(\"div\",{style:U,ref:s,onTouchStart:c,onMouseDown:c},e.createElement(\"style\",null,\"\\n        .saturation-white {\\n          background: -webkit-linear-gradient(to right, #fff, rgba(255,255,255,0));\\n          background: linear-gradient(to right, #fff, rgba(255,255,255,0));\\n        }\\n        .saturation-black {\\n          background: -webkit-linear-gradient(to top, #000, rgba(0,0,0,0));\\n          background: linear-gradient(to top, #000, rgba(0,0,0,0));\\n        }\\n        \"),e.createElement(\"div\",{style:B,className:\"saturation-white\"},e.createElement(\"div\",{style:B,className:\"saturation-black\"}),e.createElement(\"div\",{style:N},e.createElement(\"div\",{style:z}))))})),M=e.memo((function(t){var a=t.colors,i=t.onClick,s=t.currentColor,c=r([]),u=c[0],f=c[1];o((function(){var e=JSON.parse(window.localStorage.getItem(\"rpc-presets\")||\"[]\");Array.isArray(e)&&f(e)}),[]);var h=n((function(){var e=l(l([],u,!0),[s],!1);window.localStorage.setItem(\"rpc-presets\",JSON.stringify(e)),f(e)}),[a,s]);return e.createElement(I,{onClick:i,colors:l(l([],a,!0),u,!0),onAdd:h})})),L={background:\"var(--rpc-background)\",boxShadow:\"var(--rpc-box-shadow)\",borderRadius:\"var(--rpc-border-radius)\",width:\"var(--rpc-width)\",minWidth:\"280px\",border:\"1px solid var(--rpc-border-color)\"},T={position:\"relative\",width:\"25px\",height:\"25px\",borderRadius:\"var(--rpc-border-radius)\",overflow:\"hidden\",boxShadow:\"inset 0 0 2px 0px rgba(0, 0, 0, .2)\",backgroundImage:'url(\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAC9JREFUOBFjZGBgEAFifOANPkkmfJLEyI0awMAw8GHASERU4U0nA++FURdQISEBAFeUATP+HuV8AAAAAElFTkSuQmCC\")',backgroundPosition:\"left center\"},P={background:\"var(--rpc-input-background)\",borderRadius:\"var(--rpc-border-radius)\"},j={position:\"absolute\",top:\"0px\",right:\"0px\",bottom:\"0px\",left:\"0px\",background:\"var(--rpc-rgba)\",zIndex:1,border:\"1px solid var(--rpc-border-color)\",borderRadius:\"var(--rpc-border-radius)\"},G={margin:\"0.4rem 0.4rem\",width:\"calc(100% - 0.8rem)\",display:\"flex\",alignItems:\"center\",justifyContent:\"center\",flexWrap:\"wrap\"},Q={flexGrow:2,marginLeft:\"0.4rem\"},D={margin:\"0 0.3rem 0.3rem\",display:\"flex\",alignItems:\"center\",justifyContent:\"space-between\"},V={display:\"flex\",alignItems:\"center\",color:\"var(--rpc-color)\",border:\"1px solid var(--rpc-border-color)\",borderRadius:\"var(--rpc-border-radius)\",margin:\"0.1rem\",background:\"var(--rpc-input-background)\"},q={width:\"45px\",background:\"none\",color:\"var(--rpc-color)\",border:\"none\",borderRadius:\"var(--rpc-border-radius)\",padding:\"5px\",fontSize:\"10px\",margin:\"0\",textTransform:\"uppercase\",boxSizing:\"border-box\"},J={paddingLeft:\"5px\",fontSize:\"10px\",color:\"var(--rpc-borderColor)\"},X={display:\"flex\",flexDirection:\"column\",alignItems:\"center\",fontFamily:\"sans-serif\",fontSize:\"10px\",color:\"var(--rpc-color)\"},$=e.memo((function(r){var t=r.name,n=r.value,a=r.type,o=void 0===a?\"text\":a,l=r.label,s=r.prefix,c=r.min,u=r.max,f=r.step,h=r.maxLength,d=r.size,g=void 0===d?\"small\":d,p=r.onChange,b=r.onBlur,m=\"small\"===g?\"42px\":\"54px\";return e.createElement(\"label\",{style:X},e.createElement(\"div\",{style:V},s&&e.createElement(\"span\",{style:J},s),e.createElement(\"input\",{autoComplete:\"off\",type:o,name:t,value:n,onChange:function(e){return p(e.target.value)},onBlur:function(e){if(b)return b(e.target.value);p(e.target.value)},style:i(i({},q),{width:m}),spellCheck:!1,maxLength:h,min:c,max:u,step:f})),e.createElement(\"span\",null,l))})),Y=e.memo((function(t){var a=t.theme,l=t.color,s=t.presets,d=t.onChange,g=t.hideAlpha,p=t.hideInputs,b=t.className,m=t.combinations,x=r(u(l)),A=x[0],y=x[1];o((function(){y(u(l))}),[l]);var _=n((function(e){var r=u(e);y(r),d&&d(r)}),[A]),w=n((function(e){return _(i(i({},A.rgb),{a:e}))}),[A]),E=A.rgb,S=A.hsl,R=A.hsv,C=A.hex,H=A.alpha,F={\"--rpc-background\":(null==a?void 0:a.background)||h.light.background,\"--rpc-input-background\":(null==a?void 0:a.inputBackground)||h.light.inputBackground,\"--rpc-color\":(null==a?void 0:a.color)||h.light.color,\"--rpc-border-color\":(null==a?void 0:a.borderColor)||h.light.borderColor,\"--rpc-border-radius\":(null==a?void 0:a.borderRadius)||h.light.borderRadius,\"--rpc-box-shadow\":(null==a?void 0:a.boxShadow)||h.light.boxShadow,\"--rpc-width\":(null==a?void 0:a.width)||h.light.width},B={\"--rpc-hue\":S.h,\"--rpc-red\":E.r,\"--rpc-green\":E.g,\"--rpc-blue\":E.b,\"--rpc-hex\":C,\"--rpc-alpha\":H,\"--rpc-rgba\":\"rgba(\".concat(E.r,\", \").concat(E.g,\", \").concat(E.b,\", \").concat(H,\")\"),\"--rpc-hue-pointer\":\"\".concat(100*S.h/360,\"%\"),\"--rpc-alpha-pointer\":\"\".concat(100*H,\"%\"),\"--rpc-saturation-pointer-top\":\"calc(\".concat(-100*R.v+100,\"% - 6px)\"),\"--rpc-saturation-pointer-left\":\"calc(\".concat(100*R.s,\"% - 6px)\")},U=function(e,r){var t;if(!(\"\"===r||r.length>3)){var n=\"a\"===e?parseInt(r)/100:parseInt(r);_(i(i({},E),((t={})[e]=n,t)))}};return e.createElement(\"div\",{style:i(i(i({},F),L),B),className:b},e.createElement(O,{hsl:S,onChange:_}),e.createElement(\"div\",{style:G},e.createElement(\"div\",{style:P},e.createElement(\"div\",{style:T},e.createElement(\"div\",{style:j}))),e.createElement(\"div\",{style:Q},e.createElement(v,{hsl:S,onChange:_}),!g&&e.createElement(k,{onChange:w}))),!p&&e.createElement(\"div\",{style:D},e.createElement($,{type:\"text\",name:\"hex\",label:\"Hex\",size:\"large\",prefix:\"#\",onChange:function(e){if(/^[0-9A-Fa-f]+$/.test(e)){var r=c(\"#\".concat(e));y({hex:\"#\".concat(e),rgb:r.toRgb(),hsl:r.toHsl(),hsv:r.toHsv(),alpha:r.getAlpha()})}},onBlur:_,maxLength:6,value:C.replace(\"#\",\"\")}),e.createElement(\"div\",{style:{display:\"flex\"}},e.createElement($,{value:E.r,label:\"R\",name:\"red\",type:\"number\",min:0,max:255,step:1,onChange:function(e){return U(\"r\",e)}}),e.createElement($,{value:E.g,label:\"G\",name:\"green\",type:\"number\",min:0,max:255,step:1,onChange:function(e){return U(\"g\",e)}}),e.createElement($,{value:E.b,label:\"B\",name:\"blue\",type:\"number\",min:0,max:255,step:1,onChange:function(e){return U(\"b\",e)}}),!g&&e.createElement($,{value:100*E.a,label:\"Alpha\",name:\"alpha\",type:\"number\",min:0,max:100,step:1,onChange:function(e){return U(\"a\",e)}}))),s&&e.createElement(M,{colors:s,onClick:_,currentColor:A.rgb}),m&&e.createElement(I,{colors:f(A,m),onClick:_}))}));export{Y as ColorPicker,Y as default,u as initColor,h as themes};\n"], "mappings": ";;;;;;;;AAAA,mBAA6F;AAAQ,IAAI,IAAE,WAAU;AAAC,UAAO,IAAE,OAAO,UAAQ,SAASA,IAAE;AAAC,aAAQC,IAAEC,KAAE,GAAEC,KAAE,UAAU,QAAOD,KAAEC,IAAED,KAAI,UAAQE,MAAKH,KAAE,UAAUC,EAAC,EAAE,QAAO,UAAU,eAAe,KAAKD,IAAEG,EAAC,MAAIJ,GAAEI,EAAC,IAAEH,GAAEG,EAAC;AAAG,WAAOJ;AAAA,EAAC,GAAG,MAAM,MAAK,SAAS;AAAC;AAAE,SAAS,EAAEA,IAAEC,IAAEC,IAAE;AAAC,MAAGA,MAAG,MAAI,UAAU,OAAO,UAAQC,IAAEC,KAAE,GAAEC,KAAEJ,GAAE,QAAOG,KAAEC,IAAED,KAAI,EAACD,MAAGC,MAAKH,OAAIE,OAAIA,KAAE,MAAM,UAAU,MAAM,KAAKF,IAAE,GAAEG,EAAC,IAAGD,GAAEC,EAAC,IAAEH,GAAEG,EAAC;AAAG,SAAOJ,GAAE,OAAOG,MAAG,MAAM,UAAU,MAAM,KAAKF,EAAC,CAAC;AAAC;AAAqD,IAAI;AAAJ,IAAM,KAAG,SAASK,IAAE;AAAC,GAAC,SAASC,IAAE;AAAC,QAAIC,KAAE,QAAOC,KAAE,QAAOC,KAAE,GAAEC,KAAEJ,GAAE,OAAMK,KAAEL,GAAE,KAAIM,KAAEN,GAAE,KAAIO,KAAEP,GAAE;AAAO,aAASQ,GAAET,IAAEQ,IAAE;AAAC,UAAGA,KAAEA,MAAG,CAAC,IAAGR,KAAEA,MAAG,eAAcS,GAAE,QAAOT;AAAE,UAAG,EAAE,gBAAgBS,IAAG,QAAO,IAAIA,GAAET,IAAEQ,EAAC;AAAE,UAAIE,KAAE,SAASV,IAAE;AAAC,YAAII,IAAEC,IAAEG,IAAEC,KAAE,EAAC,GAAE,GAAE,GAAE,GAAE,GAAE,EAAC,GAAEC,KAAE,GAAEC,KAAE,MAAKC,KAAE,MAAKC,KAAE,MAAKC,KAAE,OAAGC,KAAE;AAAG,eAAM,YAAU,OAAOf,OAAIA,KAAE,SAASA,IAAE;AAAC,UAAAA,KAAEA,GAAE,QAAQE,IAAE,EAAE,EAAE,QAAQC,IAAE,EAAE,EAAE,YAAY;AAAE,cAAIF,IAAEG,KAAE;AAAG,cAAGY,GAAEhB,EAAC,EAAE,CAAAA,KAAEgB,GAAEhB,EAAC,GAAEI,KAAE;AAAA,mBAAW,iBAAeJ,GAAE,QAAM,EAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,QAAO,OAAM;AAAE,kBAAOC,KAAEgB,GAAE,IAAI,KAAKjB,EAAC,KAAG,EAAC,GAAEC,GAAE,CAAC,GAAE,GAAEA,GAAE,CAAC,GAAE,GAAEA,GAAE,CAAC,EAAC,KAAGA,KAAEgB,GAAE,KAAK,KAAKjB,EAAC,KAAG,EAAC,GAAEC,GAAE,CAAC,GAAE,GAAEA,GAAE,CAAC,GAAE,GAAEA,GAAE,CAAC,GAAE,GAAEA,GAAE,CAAC,EAAC,KAAGA,KAAEgB,GAAE,IAAI,KAAKjB,EAAC,KAAG,EAAC,GAAEC,GAAE,CAAC,GAAE,GAAEA,GAAE,CAAC,GAAE,GAAEA,GAAE,CAAC,EAAC,KAAGA,KAAEgB,GAAE,KAAK,KAAKjB,EAAC,KAAG,EAAC,GAAEC,GAAE,CAAC,GAAE,GAAEA,GAAE,CAAC,GAAE,GAAEA,GAAE,CAAC,GAAE,GAAEA,GAAE,CAAC,EAAC,KAAGA,KAAEgB,GAAE,IAAI,KAAKjB,EAAC,KAAG,EAAC,GAAEC,GAAE,CAAC,GAAE,GAAEA,GAAE,CAAC,GAAE,GAAEA,GAAE,CAAC,EAAC,KAAGA,KAAEgB,GAAE,KAAK,KAAKjB,EAAC,KAAG,EAAC,GAAEC,GAAE,CAAC,GAAE,GAAEA,GAAE,CAAC,GAAE,GAAEA,GAAE,CAAC,GAAE,GAAEA,GAAE,CAAC,EAAC,KAAGA,KAAEgB,GAAE,KAAK,KAAKjB,EAAC,KAAG,EAAC,GAAEkB,GAAEjB,GAAE,CAAC,CAAC,GAAE,GAAEiB,GAAEjB,GAAE,CAAC,CAAC,GAAE,GAAEiB,GAAEjB,GAAE,CAAC,CAAC,GAAE,GAAEkB,GAAElB,GAAE,CAAC,CAAC,GAAE,QAAOG,KAAE,SAAO,OAAM,KAAGH,KAAEgB,GAAE,KAAK,KAAKjB,EAAC,KAAG,EAAC,GAAEkB,GAAEjB,GAAE,CAAC,CAAC,GAAE,GAAEiB,GAAEjB,GAAE,CAAC,CAAC,GAAE,GAAEiB,GAAEjB,GAAE,CAAC,CAAC,GAAE,QAAOG,KAAE,SAAO,MAAK,KAAGH,KAAEgB,GAAE,KAAK,KAAKjB,EAAC,KAAG,EAAC,GAAEkB,GAAEjB,GAAE,CAAC,IAAE,KAAGA,GAAE,CAAC,CAAC,GAAE,GAAEiB,GAAEjB,GAAE,CAAC,IAAE,KAAGA,GAAE,CAAC,CAAC,GAAE,GAAEiB,GAAEjB,GAAE,CAAC,IAAE,KAAGA,GAAE,CAAC,CAAC,GAAE,GAAEkB,GAAElB,GAAE,CAAC,IAAE,KAAGA,GAAE,CAAC,CAAC,GAAE,QAAOG,KAAE,SAAO,OAAM,IAAE,CAAC,EAAEH,KAAEgB,GAAE,KAAK,KAAKjB,EAAC,MAAI,EAAC,GAAEkB,GAAEjB,GAAE,CAAC,IAAE,KAAGA,GAAE,CAAC,CAAC,GAAE,GAAEiB,GAAEjB,GAAE,CAAC,IAAE,KAAGA,GAAE,CAAC,CAAC,GAAE,GAAEiB,GAAEjB,GAAE,CAAC,IAAE,KAAGA,GAAE,CAAC,CAAC,GAAE,QAAOG,KAAE,SAAO,MAAK;AAAA,QAAC,EAAEJ,EAAC,IAAG,YAAU,OAAOA,OAAIoB,GAAEpB,GAAE,CAAC,KAAGoB,GAAEpB,GAAE,CAAC,KAAGoB,GAAEpB,GAAE,CAAC,KAAGI,KAAEJ,GAAE,GAAEK,KAAEL,GAAE,GAAEQ,KAAER,GAAE,GAAES,KAAE,EAAC,GAAE,MAAIY,GAAEjB,IAAE,GAAG,GAAE,GAAE,MAAIiB,GAAEhB,IAAE,GAAG,GAAE,GAAE,MAAIgB,GAAEb,IAAE,GAAG,EAAC,GAAEM,KAAE,MAAGC,KAAE,QAAM,OAAOf,GAAE,CAAC,EAAE,OAAO,EAAE,IAAE,SAAO,SAAOoB,GAAEpB,GAAE,CAAC,KAAGoB,GAAEpB,GAAE,CAAC,KAAGoB,GAAEpB,GAAE,CAAC,KAAGW,KAAEW,GAAEtB,GAAE,CAAC,GAAEY,KAAEU,GAAEtB,GAAE,CAAC,GAAES,KAAE,SAAST,IAAEE,IAAEC,IAAE;AAAC,UAAAH,KAAE,IAAEqB,GAAErB,IAAE,GAAG,GAAEE,KAAEmB,GAAEnB,IAAE,GAAG,GAAEC,KAAEkB,GAAElB,IAAE,GAAG;AAAE,cAAIC,KAAEH,GAAE,MAAMD,EAAC,GAAEK,KAAEL,KAAEI,IAAEE,KAAEH,MAAG,IAAED,KAAGK,KAAEJ,MAAG,IAAEE,KAAEH,KAAGM,KAAEL,MAAG,KAAG,IAAEE,MAAGH,KAAGO,KAAEL,KAAE;AAAE,iBAAM,EAAC,GAAE,MAAI,CAACD,IAAEI,IAAED,IAAEA,IAAEE,IAAEL,EAAC,EAAEM,EAAC,GAAE,GAAE,MAAI,CAACD,IAAEL,IAAEA,IAAEI,IAAED,IAAEA,EAAC,EAAEG,EAAC,GAAE,GAAE,MAAI,CAACH,IAAEA,IAAEE,IAAEL,IAAEA,IAAEI,EAAC,EAAEE,EAAC,EAAC;AAAA,QAAC,EAAET,GAAE,GAAEW,IAAEC,EAAC,GAAEE,KAAE,MAAGC,KAAE,SAAOK,GAAEpB,GAAE,CAAC,KAAGoB,GAAEpB,GAAE,CAAC,KAAGoB,GAAEpB,GAAE,CAAC,MAAIW,KAAEW,GAAEtB,GAAE,CAAC,GAAEa,KAAES,GAAEtB,GAAE,CAAC,GAAES,KAAE,SAAST,IAAEC,IAAEC,IAAE;AAAC,cAAIC,IAAEC,IAAEC;AAAE,mBAASC,GAAEN,IAAEC,IAAEC,IAAE;AAAC,mBAAOA,KAAE,MAAIA,MAAG,IAAGA,KAAE,MAAIA,MAAG,IAAGA,KAAE,IAAE,IAAEF,KAAE,KAAGC,KAAED,MAAGE,KAAEA,KAAE,MAAGD,KAAEC,KAAE,IAAE,IAAEF,MAAGC,KAAED,OAAI,IAAE,IAAEE,MAAG,IAAEF;AAAA,UAAC;AAAC,cAAGA,KAAEqB,GAAErB,IAAE,GAAG,GAAEC,KAAEoB,GAAEpB,IAAE,GAAG,GAAEC,KAAEmB,GAAEnB,IAAE,GAAG,GAAE,MAAID,GAAE,CAAAE,KAAEC,KAAEC,KAAEH;AAAA,eAAM;AAAC,gBAAIK,KAAEL,KAAE,MAAGA,MAAG,IAAED,MAAGC,KAAED,KAAEC,KAAED,IAAEO,KAAE,IAAEN,KAAEK;AAAE,YAAAJ,KAAEG,GAAEE,IAAED,IAAEP,KAAE,IAAE,CAAC,GAAEI,KAAEE,GAAEE,IAAED,IAAEP,EAAC,GAAEK,KAAEC,GAAEE,IAAED,IAAEP,KAAE,IAAE,CAAC;AAAA,UAAC;AAAC,iBAAM,EAAC,GAAE,MAAIG,IAAE,GAAE,MAAIC,IAAE,GAAE,MAAIC,GAAC;AAAA,QAAC,EAAEL,GAAE,GAAEW,IAAEE,EAAC,GAAEC,KAAE,MAAGC,KAAE,QAAOf,GAAE,eAAe,GAAG,MAAIU,KAAEV,GAAE,KAAIU,KAAEa,GAAEb,EAAC,GAAE,EAAC,IAAGI,IAAE,QAAOd,GAAE,UAAQe,IAAE,GAAET,GAAE,KAAIC,GAAEE,GAAE,GAAE,CAAC,CAAC,GAAE,GAAEH,GAAE,KAAIC,GAAEE,GAAE,GAAE,CAAC,CAAC,GAAE,GAAEH,GAAE,KAAIC,GAAEE,GAAE,GAAE,CAAC,CAAC,GAAE,GAAEC,GAAC;AAAA,MAAC,EAAEV,EAAC;AAAE,WAAK,iBAAeA,IAAE,KAAK,KAAGU,GAAE,GAAE,KAAK,KAAGA,GAAE,GAAE,KAAK,KAAGA,GAAE,GAAE,KAAK,KAAGA,GAAE,GAAE,KAAK,UAAQL,GAAE,MAAI,KAAK,EAAE,IAAE,KAAI,KAAK,UAAQG,GAAE,UAAQE,GAAE,QAAO,KAAK,gBAAcF,GAAE,cAAa,KAAK,KAAG,MAAI,KAAK,KAAGH,GAAE,KAAK,EAAE,IAAG,KAAK,KAAG,MAAI,KAAK,KAAGA,GAAE,KAAK,EAAE,IAAG,KAAK,KAAG,MAAI,KAAK,KAAGA,GAAE,KAAK,EAAE,IAAG,KAAK,MAAIK,GAAE,IAAG,KAAK,SAAON;AAAA,IAAG;AAAC,aAASM,GAAEV,IAAEC,IAAEC,IAAE;AAAC,MAAAF,KAAEqB,GAAErB,IAAE,GAAG,GAAEC,KAAEoB,GAAEpB,IAAE,GAAG,GAAEC,KAAEmB,GAAEnB,IAAE,GAAG;AAAE,UAAIC,IAAEC,IAAEC,KAAEE,GAAEP,IAAEC,IAAEC,EAAC,GAAEM,KAAEF,GAAEN,IAAEC,IAAEC,EAAC,GAAEO,MAAGJ,KAAEG,MAAG;AAAE,UAAGH,MAAGG,GAAE,CAAAL,KAAEC,KAAE;AAAA,WAAM;AAAC,YAAIM,KAAEL,KAAEG;AAAE,gBAAOJ,KAAEK,KAAE,MAAGC,MAAG,IAAEL,KAAEG,MAAGE,MAAGL,KAAEG,KAAGH,IAAE;AAAA,UAAC,KAAKL;AAAE,YAAAG,MAAGF,KAAEC,MAAGQ,MAAGT,KAAEC,KAAE,IAAE;AAAG;AAAA,UAAM,KAAKD;AAAE,YAAAE,MAAGD,KAAEF,MAAGU,KAAE;AAAE;AAAA,UAAM,KAAKR;AAAE,YAAAC,MAAGH,KAAEC,MAAGS,KAAE;AAAA,QAAC;AAAC,QAAAP,MAAG;AAAA,MAAC;AAAC,aAAM,EAAC,GAAEA,IAAE,GAAEC,IAAE,GAAEK,GAAC;AAAA,IAAC;AAAC,aAASE,GAAEX,IAAEC,IAAEC,IAAE;AAAC,MAAAF,KAAEqB,GAAErB,IAAE,GAAG,GAAEC,KAAEoB,GAAEpB,IAAE,GAAG,GAAEC,KAAEmB,GAAEnB,IAAE,GAAG;AAAE,UAAIC,IAAEC,IAAEC,KAAEE,GAAEP,IAAEC,IAAEC,EAAC,GAAEM,KAAEF,GAAEN,IAAEC,IAAEC,EAAC,GAAEO,KAAEJ,IAAEK,KAAEL,KAAEG;AAAE,UAAGJ,KAAE,MAAIC,KAAE,IAAEK,KAAEL,IAAEA,MAAGG,GAAE,CAAAL,KAAE;AAAA,WAAM;AAAC,gBAAOE,IAAE;AAAA,UAAC,KAAKL;AAAE,YAAAG,MAAGF,KAAEC,MAAGQ,MAAGT,KAAEC,KAAE,IAAE;AAAG;AAAA,UAAM,KAAKD;AAAE,YAAAE,MAAGD,KAAEF,MAAGU,KAAE;AAAE;AAAA,UAAM,KAAKR;AAAE,YAAAC,MAAGH,KAAEC,MAAGS,KAAE;AAAA,QAAC;AAAC,QAAAP,MAAG;AAAA,MAAC;AAAC,aAAM,EAAC,GAAEA,IAAE,GAAEC,IAAE,GAAEK,GAAC;AAAA,IAAC;AAAC,aAASG,GAAEZ,IAAEC,IAAEC,IAAEC,IAAE;AAAC,UAAIC,KAAE,CAACoB,GAAEnB,GAAEL,EAAC,EAAE,SAAS,EAAE,CAAC,GAAEwB,GAAEnB,GAAEJ,EAAC,EAAE,SAAS,EAAE,CAAC,GAAEuB,GAAEnB,GAAEH,EAAC,EAAE,SAAS,EAAE,CAAC,CAAC;AAAE,aAAOC,MAAGC,GAAE,CAAC,EAAE,OAAO,CAAC,KAAGA,GAAE,CAAC,EAAE,OAAO,CAAC,KAAGA,GAAE,CAAC,EAAE,OAAO,CAAC,KAAGA,GAAE,CAAC,EAAE,OAAO,CAAC,KAAGA,GAAE,CAAC,EAAE,OAAO,CAAC,KAAGA,GAAE,CAAC,EAAE,OAAO,CAAC,IAAEA,GAAE,CAAC,EAAE,OAAO,CAAC,IAAEA,GAAE,CAAC,EAAE,OAAO,CAAC,IAAEA,GAAE,CAAC,EAAE,OAAO,CAAC,IAAEA,GAAE,KAAK,EAAE;AAAA,IAAC;AAAC,aAASS,GAAEb,IAAEC,IAAEC,IAAEC,IAAE;AAAC,aAAM,CAACqB,GAAEC,GAAEtB,EAAC,CAAC,GAAEqB,GAAEnB,GAAEL,EAAC,EAAE,SAAS,EAAE,CAAC,GAAEwB,GAAEnB,GAAEJ,EAAC,EAAE,SAAS,EAAE,CAAC,GAAEuB,GAAEnB,GAAEH,EAAC,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE;AAAA,IAAC;AAAC,aAASY,GAAEd,IAAEC,IAAE;AAAC,MAAAA,KAAE,MAAIA,KAAE,IAAEA,MAAG;AAAG,UAAIC,KAAEO,GAAET,EAAC,EAAE,MAAM;AAAE,aAAOE,GAAE,KAAGD,KAAE,KAAIC,GAAE,IAAEwB,GAAExB,GAAE,CAAC,GAAEO,GAAEP,EAAC;AAAA,IAAC;AAAC,aAASa,GAAEf,IAAEC,IAAE;AAAC,MAAAA,KAAE,MAAIA,KAAE,IAAEA,MAAG;AAAG,UAAIC,KAAEO,GAAET,EAAC,EAAE,MAAM;AAAE,aAAOE,GAAE,KAAGD,KAAE,KAAIC,GAAE,IAAEwB,GAAExB,GAAE,CAAC,GAAEO,GAAEP,EAAC;AAAA,IAAC;AAAC,aAASyB,GAAE3B,IAAE;AAAC,aAAOS,GAAET,EAAC,EAAE,WAAW,GAAG;AAAA,IAAC;AAAC,aAAS4B,GAAE5B,IAAEC,IAAE;AAAC,MAAAA,KAAE,MAAIA,KAAE,IAAEA,MAAG;AAAG,UAAIC,KAAEO,GAAET,EAAC,EAAE,MAAM;AAAE,aAAOE,GAAE,KAAGD,KAAE,KAAIC,GAAE,IAAEwB,GAAExB,GAAE,CAAC,GAAEO,GAAEP,EAAC;AAAA,IAAC;AAAC,aAAS2B,GAAE7B,IAAEC,IAAE;AAAC,MAAAA,KAAE,MAAIA,KAAE,IAAEA,MAAG;AAAG,UAAIC,KAAEO,GAAET,EAAC,EAAE,MAAM;AAAE,aAAOE,GAAE,IAAEK,GAAE,GAAED,GAAE,KAAIJ,GAAE,IAAEG,GAAE,CAACJ,KAAE,MAAI,GAAG,CAAC,CAAC,GAAEC,GAAE,IAAEK,GAAE,GAAED,GAAE,KAAIJ,GAAE,IAAEG,GAAE,CAACJ,KAAE,MAAI,GAAG,CAAC,CAAC,GAAEC,GAAE,IAAEK,GAAE,GAAED,GAAE,KAAIJ,GAAE,IAAEG,GAAE,CAACJ,KAAE,MAAI,GAAG,CAAC,CAAC,GAAEQ,GAAEP,EAAC;AAAA,IAAC;AAAC,aAAS4B,GAAE9B,IAAEC,IAAE;AAAC,MAAAA,KAAE,MAAIA,KAAE,IAAEA,MAAG;AAAG,UAAIC,KAAEO,GAAET,EAAC,EAAE,MAAM;AAAE,aAAOE,GAAE,KAAGD,KAAE,KAAIC,GAAE,IAAEwB,GAAExB,GAAE,CAAC,GAAEO,GAAEP,EAAC;AAAA,IAAC;AAAC,aAAS6B,GAAE/B,IAAEC,IAAE;AAAC,UAAIC,KAAEO,GAAET,EAAC,EAAE,MAAM,GAAEG,MAAGD,GAAE,IAAED,MAAG;AAAI,aAAOC,GAAE,IAAEC,KAAE,IAAE,MAAIA,KAAEA,IAAEM,GAAEP,EAAC;AAAA,IAAC;AAAC,aAAS8B,GAAEhC,IAAE;AAAC,UAAIC,KAAEQ,GAAET,EAAC,EAAE,MAAM;AAAE,aAAOC,GAAE,KAAGA,GAAE,IAAE,OAAK,KAAIQ,GAAER,EAAC;AAAA,IAAC;AAAC,aAASgC,GAAEjC,IAAE;AAAC,UAAIC,KAAEQ,GAAET,EAAC,EAAE,MAAM,GAAEE,KAAED,GAAE;AAAE,aAAM,CAACQ,GAAET,EAAC,GAAES,GAAE,EAAC,IAAGP,KAAE,OAAK,KAAI,GAAED,GAAE,GAAE,GAAEA,GAAE,EAAC,CAAC,GAAEQ,GAAE,EAAC,IAAGP,KAAE,OAAK,KAAI,GAAED,GAAE,GAAE,GAAEA,GAAE,EAAC,CAAC,CAAC;AAAA,IAAC;AAAC,aAASiC,GAAElC,IAAE;AAAC,UAAIC,KAAEQ,GAAET,EAAC,EAAE,MAAM,GAAEE,KAAED,GAAE;AAAE,aAAM,CAACQ,GAAET,EAAC,GAAES,GAAE,EAAC,IAAGP,KAAE,MAAI,KAAI,GAAED,GAAE,GAAE,GAAEA,GAAE,EAAC,CAAC,GAAEQ,GAAE,EAAC,IAAGP,KAAE,OAAK,KAAI,GAAED,GAAE,GAAE,GAAEA,GAAE,EAAC,CAAC,GAAEQ,GAAE,EAAC,IAAGP,KAAE,OAAK,KAAI,GAAED,GAAE,GAAE,GAAEA,GAAE,EAAC,CAAC,CAAC;AAAA,IAAC;AAAC,aAASkC,GAAEnC,IAAE;AAAC,UAAIC,KAAEQ,GAAET,EAAC,EAAE,MAAM,GAAEE,KAAED,GAAE;AAAE,aAAM,CAACQ,GAAET,EAAC,GAAES,GAAE,EAAC,IAAGP,KAAE,MAAI,KAAI,GAAED,GAAE,GAAE,GAAEA,GAAE,EAAC,CAAC,GAAEQ,GAAE,EAAC,IAAGP,KAAE,OAAK,KAAI,GAAED,GAAE,GAAE,GAAEA,GAAE,EAAC,CAAC,CAAC;AAAA,IAAC;AAAC,aAASmC,GAAEpC,IAAEC,IAAEC,IAAE;AAAC,MAAAD,KAAEA,MAAG,GAAEC,KAAEA,MAAG;AAAG,UAAIC,KAAEM,GAAET,EAAC,EAAE,MAAM,GAAEI,KAAE,MAAIF,IAAEG,KAAE,CAACI,GAAET,EAAC,CAAC;AAAE,WAAIG,GAAE,KAAGA,GAAE,KAAGC,KAAEH,MAAG,KAAG,OAAK,KAAI,EAAEA,KAAG,CAAAE,GAAE,KAAGA,GAAE,IAAEC,MAAG,KAAIC,GAAE,KAAKI,GAAEN,EAAC,CAAC;AAAE,aAAOE;AAAA,IAAC;AAAC,aAASgC,GAAErC,IAAEC,IAAE;AAAC,MAAAA,KAAEA,MAAG;AAAE,eAAQC,KAAEO,GAAET,EAAC,EAAE,MAAM,GAAEG,KAAED,GAAE,GAAEE,KAAEF,GAAE,GAAEG,KAAEH,GAAE,GAAEI,KAAE,CAAC,GAAEC,KAAE,IAAEN,IAAEA,OAAK,CAAAK,GAAE,KAAKG,GAAE,EAAC,GAAEN,IAAE,GAAEC,IAAE,GAAEC,GAAC,CAAC,CAAC,GAAEA,MAAGA,KAAEE,MAAG;AAAE,aAAOD;AAAA,IAAC;AAAC,IAAAG,GAAE,YAAU,EAAC,QAAO,WAAU;AAAC,aAAO,KAAK,cAAc,IAAE;AAAA,IAAG,GAAE,SAAQ,WAAU;AAAC,aAAM,CAAC,KAAK,OAAO;AAAA,IAAC,GAAE,SAAQ,WAAU;AAAC,aAAO,KAAK;AAAA,IAAG,GAAE,kBAAiB,WAAU;AAAC,aAAO,KAAK;AAAA,IAAc,GAAE,WAAU,WAAU;AAAC,aAAO,KAAK;AAAA,IAAO,GAAE,UAAS,WAAU;AAAC,aAAO,KAAK;AAAA,IAAE,GAAE,eAAc,WAAU;AAAC,UAAIT,KAAE,KAAK,MAAM;AAAE,cAAO,MAAIA,GAAE,IAAE,MAAIA,GAAE,IAAE,MAAIA,GAAE,KAAG;AAAA,IAAG,GAAE,cAAa,WAAU;AAAC,UAAIA,IAAEE,IAAEC,IAAEC,KAAE,KAAK,MAAM;AAAE,aAAOJ,KAAEI,GAAE,IAAE,KAAIF,KAAEE,GAAE,IAAE,KAAID,KAAEC,GAAE,IAAE,KAAI,UAAOJ,MAAG,UAAOA,KAAE,QAAMC,GAAE,KAAKD,KAAE,SAAM,OAAM,GAAG,KAAG,UAAOE,MAAG,UAAOA,KAAE,QAAMD,GAAE,KAAKC,KAAE,SAAM,OAAM,GAAG,KAAG,UAAOC,MAAG,UAAOA,KAAE,QAAMF,GAAE,KAAKE,KAAE,SAAM,OAAM,GAAG;AAAA,IAAE,GAAE,UAAS,SAASH,IAAE;AAAC,aAAO,KAAK,KAAGuB,GAAEvB,EAAC,GAAE,KAAK,UAAQK,GAAE,MAAI,KAAK,EAAE,IAAE,KAAI;AAAA,IAAI,GAAE,OAAM,WAAU;AAAC,UAAIL,KAAEW,GAAE,KAAK,IAAG,KAAK,IAAG,KAAK,EAAE;AAAE,aAAM,EAAC,GAAE,MAAIX,GAAE,GAAE,GAAEA,GAAE,GAAE,GAAEA,GAAE,GAAE,GAAE,KAAK,GAAE;AAAA,IAAC,GAAE,aAAY,WAAU;AAAC,UAAIA,KAAEW,GAAE,KAAK,IAAG,KAAK,IAAG,KAAK,EAAE,GAAEV,KAAEI,GAAE,MAAIL,GAAE,CAAC,GAAEE,KAAEG,GAAE,MAAIL,GAAE,CAAC,GAAEG,KAAEE,GAAE,MAAIL,GAAE,CAAC;AAAE,aAAO,KAAG,KAAK,KAAG,SAAOC,KAAE,OAAKC,KAAE,QAAMC,KAAE,OAAK,UAAQF,KAAE,OAAKC,KAAE,QAAMC,KAAE,QAAM,KAAK,UAAQ;AAAA,IAAG,GAAE,OAAM,WAAU;AAAC,UAAIH,KAAEU,GAAE,KAAK,IAAG,KAAK,IAAG,KAAK,EAAE;AAAE,aAAM,EAAC,GAAE,MAAIV,GAAE,GAAE,GAAEA,GAAE,GAAE,GAAEA,GAAE,GAAE,GAAE,KAAK,GAAE;AAAA,IAAC,GAAE,aAAY,WAAU;AAAC,UAAIA,KAAEU,GAAE,KAAK,IAAG,KAAK,IAAG,KAAK,EAAE,GAAET,KAAEI,GAAE,MAAIL,GAAE,CAAC,GAAEE,KAAEG,GAAE,MAAIL,GAAE,CAAC,GAAEG,KAAEE,GAAE,MAAIL,GAAE,CAAC;AAAE,aAAO,KAAG,KAAK,KAAG,SAAOC,KAAE,OAAKC,KAAE,QAAMC,KAAE,OAAK,UAAQF,KAAE,OAAKC,KAAE,QAAMC,KAAE,QAAM,KAAK,UAAQ;AAAA,IAAG,GAAE,OAAM,SAASH,IAAE;AAAC,aAAOY,GAAE,KAAK,IAAG,KAAK,IAAG,KAAK,IAAGZ,EAAC;AAAA,IAAC,GAAE,aAAY,SAASA,IAAE;AAAC,aAAM,MAAI,KAAK,MAAMA,EAAC;AAAA,IAAC,GAAE,QAAO,SAASA,IAAE;AAAC,aAAO,SAASA,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,YAAIE,KAAE,CAACkB,GAAEnB,GAAEL,EAAC,EAAE,SAAS,EAAE,CAAC,GAAEwB,GAAEnB,GAAEJ,EAAC,EAAE,SAAS,EAAE,CAAC,GAAEuB,GAAEnB,GAAEH,EAAC,EAAE,SAAS,EAAE,CAAC,GAAEsB,GAAEC,GAAEtB,EAAC,CAAC,CAAC;AAAE,eAAOC,MAAGE,GAAE,CAAC,EAAE,OAAO,CAAC,KAAGA,GAAE,CAAC,EAAE,OAAO,CAAC,KAAGA,GAAE,CAAC,EAAE,OAAO,CAAC,KAAGA,GAAE,CAAC,EAAE,OAAO,CAAC,KAAGA,GAAE,CAAC,EAAE,OAAO,CAAC,KAAGA,GAAE,CAAC,EAAE,OAAO,CAAC,KAAGA,GAAE,CAAC,EAAE,OAAO,CAAC,KAAGA,GAAE,CAAC,EAAE,OAAO,CAAC,IAAEA,GAAE,CAAC,EAAE,OAAO,CAAC,IAAEA,GAAE,CAAC,EAAE,OAAO,CAAC,IAAEA,GAAE,CAAC,EAAE,OAAO,CAAC,IAAEA,GAAE,CAAC,EAAE,OAAO,CAAC,IAAEA,GAAE,KAAK,EAAE;AAAA,MAAC,EAAE,KAAK,IAAG,KAAK,IAAG,KAAK,IAAG,KAAK,IAAGN,EAAC;AAAA,IAAC,GAAE,cAAa,SAASA,IAAE;AAAC,aAAM,MAAI,KAAK,OAAOA,EAAC;AAAA,IAAC,GAAE,OAAM,WAAU;AAAC,aAAM,EAAC,GAAEK,GAAE,KAAK,EAAE,GAAE,GAAEA,GAAE,KAAK,EAAE,GAAE,GAAEA,GAAE,KAAK,EAAE,GAAE,GAAE,KAAK,GAAE;AAAA,IAAC,GAAE,aAAY,WAAU;AAAC,aAAO,KAAG,KAAK,KAAG,SAAOA,GAAE,KAAK,EAAE,IAAE,OAAKA,GAAE,KAAK,EAAE,IAAE,OAAKA,GAAE,KAAK,EAAE,IAAE,MAAI,UAAQA,GAAE,KAAK,EAAE,IAAE,OAAKA,GAAE,KAAK,EAAE,IAAE,OAAKA,GAAE,KAAK,EAAE,IAAE,OAAK,KAAK,UAAQ;AAAA,IAAG,GAAE,iBAAgB,WAAU;AAAC,aAAM,EAAC,GAAEA,GAAE,MAAIgB,GAAE,KAAK,IAAG,GAAG,CAAC,IAAE,KAAI,GAAEhB,GAAE,MAAIgB,GAAE,KAAK,IAAG,GAAG,CAAC,IAAE,KAAI,GAAEhB,GAAE,MAAIgB,GAAE,KAAK,IAAG,GAAG,CAAC,IAAE,KAAI,GAAE,KAAK,GAAE;AAAA,IAAC,GAAE,uBAAsB,WAAU;AAAC,aAAO,KAAG,KAAK,KAAG,SAAOhB,GAAE,MAAIgB,GAAE,KAAK,IAAG,GAAG,CAAC,IAAE,QAAMhB,GAAE,MAAIgB,GAAE,KAAK,IAAG,GAAG,CAAC,IAAE,QAAMhB,GAAE,MAAIgB,GAAE,KAAK,IAAG,GAAG,CAAC,IAAE,OAAK,UAAQhB,GAAE,MAAIgB,GAAE,KAAK,IAAG,GAAG,CAAC,IAAE,QAAMhB,GAAE,MAAIgB,GAAE,KAAK,IAAG,GAAG,CAAC,IAAE,QAAMhB,GAAE,MAAIgB,GAAE,KAAK,IAAG,GAAG,CAAC,IAAE,QAAM,KAAK,UAAQ;AAAA,IAAG,GAAE,QAAO,WAAU;AAAC,aAAO,MAAI,KAAK,KAAG,gBAAc,EAAE,KAAK,KAAG,OAAKiB,GAAE1B,GAAE,KAAK,IAAG,KAAK,IAAG,KAAK,IAAG,IAAE,CAAC,KAAG;AAAA,IAAG,GAAE,UAAS,SAASZ,IAAE;AAAC,UAAIC,KAAE,MAAIY,GAAE,KAAK,IAAG,KAAK,IAAG,KAAK,IAAG,KAAK,EAAE,GAAEX,KAAED,IAAEE,KAAE,KAAK,gBAAc,uBAAqB;AAAG,UAAGH,IAAE;AAAC,YAAII,KAAEK,GAAET,EAAC;AAAE,QAAAE,KAAE,MAAIW,GAAET,GAAE,IAAGA,GAAE,IAAGA,GAAE,IAAGA,GAAE,EAAE;AAAA,MAAC;AAAC,aAAM,gDAA8CD,KAAE,mBAAiBF,KAAE,kBAAgBC,KAAE;AAAA,IAAG,GAAE,UAAS,SAASF,IAAE;AAAC,UAAIC,KAAE,CAAC,CAACD;AAAE,MAAAA,KAAEA,MAAG,KAAK;AAAQ,UAAIE,KAAE,OAAGC,KAAE,KAAK,KAAG,KAAG,KAAK,MAAI;AAAE,aAAOF,MAAG,CAACE,MAAG,UAAQH,MAAG,WAASA,MAAG,WAASA,MAAG,WAASA,MAAG,WAASA,MAAG,WAASA,MAAG,UAAQA,OAAIE,KAAE,KAAK,YAAY,IAAG,WAASF,OAAIE,KAAE,KAAK,sBAAsB,IAAG,UAAQF,MAAG,WAASA,OAAIE,KAAE,KAAK,YAAY,IAAG,WAASF,OAAIE,KAAE,KAAK,YAAY,IAAE,IAAG,WAASF,OAAIE,KAAE,KAAK,aAAa,IAAE,IAAG,WAASF,OAAIE,KAAE,KAAK,aAAa,IAAG,WAASF,OAAIE,KAAE,KAAK,OAAO,IAAG,UAAQF,OAAIE,KAAE,KAAK,YAAY,IAAG,UAAQF,OAAIE,KAAE,KAAK,YAAY,IAAGA,MAAG,KAAK,YAAY,KAAG,WAASF,MAAG,MAAI,KAAK,KAAG,KAAK,OAAO,IAAE,KAAK,YAAY;AAAA,IAAC,GAAE,OAAM,WAAU;AAAC,aAAOS,GAAE,KAAK,SAAS,CAAC;AAAA,IAAC,GAAE,oBAAmB,SAAST,IAAEC,IAAE;AAAC,UAAIC,KAAEF,GAAE,MAAM,MAAK,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE,MAAM,KAAKC,EAAC,CAAC,CAAC;AAAE,aAAO,KAAK,KAAGC,GAAE,IAAG,KAAK,KAAGA,GAAE,IAAG,KAAK,KAAGA,GAAE,IAAG,KAAK,SAASA,GAAE,EAAE,GAAE;AAAA,IAAI,GAAE,SAAQ,WAAU;AAAC,aAAO,KAAK,mBAAmB0B,IAAE,SAAS;AAAA,IAAC,GAAE,UAAS,WAAU;AAAC,aAAO,KAAK,mBAAmBC,IAAE,SAAS;AAAA,IAAC,GAAE,QAAO,WAAU;AAAC,aAAO,KAAK,mBAAmBC,IAAE,SAAS;AAAA,IAAC,GAAE,YAAW,WAAU;AAAC,aAAO,KAAK,mBAAmBhB,IAAE,SAAS;AAAA,IAAC,GAAE,UAAS,WAAU;AAAC,aAAO,KAAK,mBAAmBC,IAAE,SAAS;AAAA,IAAC,GAAE,WAAU,WAAU;AAAC,aAAO,KAAK,mBAAmBY,IAAE,SAAS;AAAA,IAAC,GAAE,MAAK,WAAU;AAAC,aAAO,KAAK,mBAAmBI,IAAE,SAAS;AAAA,IAAC,GAAE,mBAAkB,SAAS/B,IAAEC,IAAE;AAAC,aAAOD,GAAE,MAAM,MAAK,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE,MAAM,KAAKC,EAAC,CAAC,CAAC;AAAA,IAAC,GAAE,WAAU,WAAU;AAAC,aAAO,KAAK,kBAAkBmC,IAAE,SAAS;AAAA,IAAC,GAAE,YAAW,WAAU;AAAC,aAAO,KAAK,kBAAkBJ,IAAE,SAAS;AAAA,IAAC,GAAE,eAAc,WAAU;AAAC,aAAO,KAAK,kBAAkBK,IAAE,SAAS;AAAA,IAAC,GAAE,iBAAgB,WAAU;AAAC,aAAO,KAAK,kBAAkBF,IAAE,SAAS;AAAA,IAAC,GAAE,OAAM,WAAU;AAAC,aAAO,KAAK,kBAAkBF,IAAE,SAAS;AAAA,IAAC,GAAE,QAAO,WAAU;AAAC,aAAO,KAAK,kBAAkBC,IAAE,SAAS;AAAA,IAAC,EAAC,GAAEzB,GAAE,YAAU,SAAST,IAAEC,IAAE;AAAC,UAAG,YAAU,OAAOD,IAAE;AAAC,YAAIE,KAAE,CAAC;AAAE,iBAAQC,MAAKH,GAAE,CAAAA,GAAE,eAAeG,EAAC,MAAID,GAAEC,EAAC,IAAE,QAAMA,KAAEH,GAAEG,EAAC,IAAEmB,GAAEtB,GAAEG,EAAC,CAAC;AAAG,QAAAH,KAAEE;AAAA,MAAC;AAAC,aAAOO,GAAET,IAAEC,EAAC;AAAA,IAAC,GAAEQ,GAAE,SAAO,SAAST,IAAEC,IAAE;AAAC,aAAM,EAAE,CAACD,MAAG,CAACC,OAAIQ,GAAET,EAAC,EAAE,YAAY,KAAGS,GAAER,EAAC,EAAE,YAAY;AAAA,IAAC,GAAEQ,GAAE,SAAO,WAAU;AAAC,aAAOA,GAAE,UAAU,EAAC,GAAED,GAAE,GAAE,GAAEA,GAAE,GAAE,GAAEA,GAAE,EAAC,CAAC;AAAA,IAAC,GAAEC,GAAE,MAAI,SAAST,IAAEC,IAAEC,IAAE;AAAC,MAAAA,KAAE,MAAIA,KAAE,IAAEA,MAAG;AAAG,UAAIC,KAAEM,GAAET,EAAC,EAAE,MAAM,GAAEI,KAAEK,GAAER,EAAC,EAAE,MAAM,GAAEI,KAAEH,KAAE;AAAI,aAAOO,GAAE,EAAC,IAAGL,GAAE,IAAED,GAAE,KAAGE,KAAEF,GAAE,GAAE,IAAGC,GAAE,IAAED,GAAE,KAAGE,KAAEF,GAAE,GAAE,IAAGC,GAAE,IAAED,GAAE,KAAGE,KAAEF,GAAE,GAAE,IAAGC,GAAE,IAAED,GAAE,KAAGE,KAAEF,GAAE,EAAC,CAAC;AAAA,IAAC,GAAEM,GAAE,cAAY,SAAST,IAAEE,IAAE;AAAC,UAAIC,KAAEM,GAAET,EAAC,GAAEI,KAAEK,GAAEP,EAAC;AAAE,cAAOD,GAAE,IAAIE,GAAE,aAAa,GAAEC,GAAE,aAAa,CAAC,IAAE,SAAMH,GAAE,IAAIE,GAAE,aAAa,GAAEC,GAAE,aAAa,CAAC,IAAE;AAAA,IAAI,GAAEK,GAAE,aAAW,SAAST,IAAEC,IAAEC,IAAE;AAAC,UAAIC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,KAAEC,GAAE,YAAYT,IAAEC,EAAC;AAAE,cAAOG,KAAE,QAAIC,KAAEH,IAAEI,OAAID,KAAEA,MAAG,EAAC,OAAM,MAAK,MAAK,QAAO,GAAG,SAAO,MAAM,YAAY,GAAEE,MAAGF,GAAE,QAAM,SAAS,YAAY,GAAE,SAAOC,MAAG,UAAQA,OAAIA,KAAE,OAAM,YAAUC,MAAG,YAAUA,OAAIA,KAAE,UAASJ,KAAE,EAAC,OAAMG,IAAE,MAAKC,GAAC,GAAG,QAAMJ,GAAE,MAAK;AAAA,QAAC,KAAI;AAAA,QAAU,KAAI;AAAW,UAAAC,KAAEI,MAAG;AAAI;AAAA,QAAM,KAAI;AAAU,UAAAJ,KAAEI,MAAG;AAAE;AAAA,QAAM,KAAI;AAAW,UAAAJ,KAAEI,MAAG;AAAA,MAAC;AAAC,aAAOJ;AAAA,IAAC,GAAEK,GAAE,eAAa,SAAST,IAAEC,IAAEC,IAAE;AAAC,UAAIC,IAAEC,IAAEC,IAAEC,IAAEC,KAAE,MAAKC,KAAE;AAAE,MAAAJ,MAAGF,KAAEA,MAAG,CAAC,GAAG,uBAAsBG,KAAEH,GAAE,OAAMI,KAAEJ,GAAE;AAAK,eAAQQ,KAAE,GAAEA,KAAET,GAAE,QAAOS,KAAI,EAACP,KAAEM,GAAE,YAAYT,IAAEC,GAAES,EAAC,CAAC,KAAGF,OAAIA,KAAEL,IAAEI,KAAEE,GAAER,GAAES,EAAC,CAAC;AAAG,aAAOD,GAAE,WAAWT,IAAEO,IAAE,EAAC,OAAMF,IAAE,MAAKC,GAAC,CAAC,KAAG,CAACF,KAAEG,MAAGL,GAAE,wBAAsB,OAAGO,GAAE,aAAaT,IAAE,CAAC,QAAO,MAAM,GAAEE,EAAC;AAAA,IAAE;AAAE,QAAIc,KAAEP,GAAE,QAAM,EAAC,WAAU,UAAS,cAAa,UAAS,MAAK,OAAM,YAAW,UAAS,OAAM,UAAS,OAAM,UAAS,QAAO,UAAS,OAAM,OAAM,gBAAe,UAAS,MAAK,OAAM,YAAW,UAAS,OAAM,UAAS,WAAU,UAAS,aAAY,UAAS,WAAU,UAAS,YAAW,UAAS,WAAU,UAAS,OAAM,UAAS,gBAAe,UAAS,UAAS,UAAS,SAAQ,UAAS,MAAK,OAAM,UAAS,UAAS,UAAS,UAAS,eAAc,UAAS,UAAS,UAAS,WAAU,UAAS,UAAS,UAAS,WAAU,UAAS,aAAY,UAAS,gBAAe,UAAS,YAAW,UAAS,YAAW,UAAS,SAAQ,UAAS,YAAW,UAAS,cAAa,UAAS,eAAc,UAAS,eAAc,UAAS,eAAc,UAAS,eAAc,UAAS,YAAW,UAAS,UAAS,UAAS,aAAY,UAAS,SAAQ,UAAS,SAAQ,UAAS,YAAW,UAAS,WAAU,UAAS,aAAY,UAAS,aAAY,UAAS,SAAQ,OAAM,WAAU,UAAS,YAAW,UAAS,MAAK,UAAS,WAAU,UAAS,MAAK,UAAS,OAAM,UAAS,aAAY,UAAS,MAAK,UAAS,UAAS,UAAS,SAAQ,UAAS,WAAU,UAAS,QAAO,UAAS,OAAM,UAAS,OAAM,UAAS,UAAS,UAAS,eAAc,UAAS,WAAU,UAAS,cAAa,UAAS,WAAU,UAAS,YAAW,UAAS,WAAU,UAAS,sBAAqB,UAAS,WAAU,UAAS,YAAW,UAAS,WAAU,UAAS,WAAU,UAAS,aAAY,UAAS,eAAc,UAAS,cAAa,UAAS,gBAAe,OAAM,gBAAe,OAAM,gBAAe,UAAS,aAAY,UAAS,MAAK,OAAM,WAAU,UAAS,OAAM,UAAS,SAAQ,OAAM,QAAO,UAAS,kBAAiB,UAAS,YAAW,UAAS,cAAa,UAAS,cAAa,UAAS,gBAAe,UAAS,iBAAgB,UAAS,mBAAkB,UAAS,iBAAgB,UAAS,iBAAgB,UAAS,cAAa,UAAS,WAAU,UAAS,WAAU,UAAS,UAAS,UAAS,aAAY,UAAS,MAAK,UAAS,SAAQ,UAAS,OAAM,UAAS,WAAU,UAAS,QAAO,UAAS,WAAU,UAAS,QAAO,UAAS,eAAc,UAAS,WAAU,UAAS,eAAc,UAAS,eAAc,UAAS,YAAW,UAAS,WAAU,UAAS,MAAK,UAAS,MAAK,UAAS,MAAK,UAAS,YAAW,UAAS,QAAO,UAAS,eAAc,UAAS,KAAI,OAAM,WAAU,UAAS,WAAU,UAAS,aAAY,UAAS,QAAO,UAAS,YAAW,UAAS,UAAS,UAAS,UAAS,UAAS,QAAO,UAAS,QAAO,UAAS,SAAQ,UAAS,WAAU,UAAS,WAAU,UAAS,WAAU,UAAS,MAAK,UAAS,aAAY,UAAS,WAAU,UAAS,KAAI,UAAS,MAAK,UAAS,SAAQ,UAAS,QAAO,UAAS,WAAU,UAAS,QAAO,UAAS,OAAM,UAAS,OAAM,OAAM,YAAW,UAAS,QAAO,OAAM,aAAY,SAAQ,GAAE6B,KAAE7B,GAAE,WAAS,SAAST,IAAE;AAAC,UAAIC,KAAE,CAAC;AAAE,eAAQC,MAAKF,GAAE,CAAAA,GAAE,eAAeE,EAAC,MAAID,GAAED,GAAEE,EAAC,CAAC,IAAEA;AAAG,aAAOD;AAAA,IAAC,EAAEe,EAAC;AAAE,aAASO,GAAEvB,IAAE;AAAC,aAAOA,KAAE,WAAWA,EAAC,IAAG,MAAMA,EAAC,KAAGA,KAAE,KAAGA,KAAE,OAAKA,KAAE,IAAGA;AAAA,IAAC;AAAC,aAASqB,GAAErB,IAAEE,IAAE;AAAC,OAAC,SAASF,IAAE;AAAC,eAAM,YAAU,OAAOA,MAAG,MAAIA,GAAE,QAAQ,GAAG,KAAG,MAAI,WAAWA,EAAC;AAAA,MAAC,GAAGA,EAAC,MAAIA,KAAE;AAAQ,UAAIG,KAAE,SAASH,IAAE;AAAC,eAAM,YAAU,OAAOA,MAAG,MAAIA,GAAE,QAAQ,GAAG;AAAA,MAAC,EAAEA,EAAC;AAAE,aAAOA,KAAEM,GAAEJ,IAAEK,GAAE,GAAE,WAAWP,EAAC,CAAC,CAAC,GAAEG,OAAIH,KAAE,SAASA,KAAEE,IAAE,EAAE,IAAE,MAAKD,GAAE,IAAID,KAAEE,EAAC,IAAE,OAAK,IAAEF,KAAEE,KAAE,WAAWA,EAAC;AAAA,IAAC;AAAC,aAASwB,GAAE1B,IAAE;AAAC,aAAOM,GAAE,GAAEC,GAAE,GAAEP,EAAC,CAAC;AAAA,IAAC;AAAC,aAASkB,GAAElB,IAAE;AAAC,aAAO,SAASA,IAAE,EAAE;AAAA,IAAC;AAAC,aAASwB,GAAExB,IAAE;AAAC,aAAO,KAAGA,GAAE,SAAO,MAAIA,KAAE,KAAGA;AAAA,IAAC;AAAC,aAASsB,GAAEtB,IAAE;AAAC,aAAOA,MAAG,MAAIA,KAAE,MAAIA,KAAE,MAAKA;AAAA,IAAC;AAAC,aAASyB,GAAEzB,IAAE;AAAC,aAAOC,GAAE,MAAM,MAAI,WAAWD,EAAC,CAAC,EAAE,SAAS,EAAE;AAAA,IAAC;AAAC,aAASmB,GAAEnB,IAAE;AAAC,aAAOkB,GAAElB,EAAC,IAAE;AAAA,IAAG;AAAC,QAAIuC,IAAEC,IAAEC,IAAExB,MAAGuB,KAAE,iBAAeD,KAAE,gDAA8C,eAAaA,KAAE,eAAaA,KAAE,aAAYE,KAAE,gBAAcF,KAAE,eAAaA,KAAE,eAAaA,KAAE,eAAaA,KAAE,aAAY,EAAC,UAAS,IAAI,OAAOA,EAAC,GAAE,KAAI,IAAI,OAAO,QAAMC,EAAC,GAAE,MAAK,IAAI,OAAO,SAAOC,EAAC,GAAE,KAAI,IAAI,OAAO,QAAMD,EAAC,GAAE,MAAK,IAAI,OAAO,SAAOC,EAAC,GAAE,KAAI,IAAI,OAAO,QAAMD,EAAC,GAAE,MAAK,IAAI,OAAO,SAAOC,EAAC,GAAE,MAAK,wDAAuD,MAAK,wDAAuD,MAAK,wEAAuE,MAAK,uEAAsE;AAAG,aAASrB,GAAEpB,IAAE;AAAC,aAAM,CAAC,CAACiB,GAAE,SAAS,KAAKjB,EAAC;AAAA,IAAC;AAAC,IAAAA,GAAE,UAAQA,GAAE,UAAQS,KAAE,OAAO,YAAUA;AAAA,EAAC,EAAE,IAAI;AAAC,EAAE,IAAE,EAAC,SAAQ,CAAC,EAAC,GAAE,EAAE,OAAO,GAAE,EAAE;AAA7oc,IAAspc,IAAE,SAAST,IAAE;AAAC,MAAIC,KAAE,EAAED,EAAC;AAAE,SAAOC,GAAE,QAAQ,IAAE,EAAC,KAAIA,GAAE,YAAY,GAAE,KAAIA,GAAE,MAAM,GAAE,KAAIA,GAAE,MAAM,GAAE,KAAIA,GAAE,MAAM,GAAE,OAAMA,GAAE,SAAS,EAAC,IAAE,EAAC,KAAI,WAAU,KAAI,EAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,EAAC,GAAE,KAAI,EAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,EAAC,GAAE,KAAI,EAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,EAAC,GAAE,OAAM,EAAC;AAAC;AAA92c,IAAg3c,IAAE,SAASD,IAAEC,IAAE;AAAC,MAAIC,KAAEF,GAAE,KAAIG,KAAE,EAAED,EAAC,GAAEE,KAAE,CAAC;AAAE,UAAO,YAAU,OAAOH,KAAE,CAACA,EAAC,IAAEA,IAAG,QAAS,SAASD,IAAE;AAAC,WAAM,gBAAcA,KAAEG,GAAE,UAAU,EAAE,QAAS,SAASH,IAAE;AAAC,aAAOI,GAAE,KAAKJ,GAAE,YAAY,CAAC;AAAA,IAAC,CAAE,IAAE,oBAAkBA,KAAEG,GAAE,cAAc,EAAE,QAAS,SAASH,IAAE;AAAC,aAAOI,GAAE,KAAKJ,GAAE,YAAY,CAAC;AAAA,IAAC,CAAE,IAAE,sBAAoBA,KAAEG,GAAE,gBAAgB,EAAE,QAAS,SAASH,IAAE;AAAC,aAAOI,GAAE,KAAKJ,GAAE,YAAY,CAAC;AAAA,IAAC,CAAE,IAAE,aAAWA,KAAEG,GAAE,OAAO,EAAE,QAAS,SAASH,IAAE;AAAC,aAAOI,GAAE,KAAKJ,GAAE,YAAY,CAAC;AAAA,IAAC,CAAE,IAAE,YAAUA,KAAEG,GAAE,MAAM,EAAE,QAAS,SAASH,IAAE;AAAC,aAAOI,GAAE,KAAKJ,GAAE,YAAY,CAAC;AAAA,IAAC,CAAE,IAAEI,GAAE,KAAKD,GAAE,WAAW,EAAE,YAAY,CAAC;AAAA,EAAC,CAAE,GAAEC;AAAC;AAA56d,IAA86d,IAAE,EAAC,OAAM,EAAC,YAAW,QAAO,iBAAgB,WAAU,OAAM,WAAU,aAAY,WAAU,cAAa,OAAM,WAAU,mCAAkC,OAAM,QAAO,GAAE,MAAK,EAAC,YAAW,0BAAyB,iBAAgB,WAAU,OAAM,WAAU,cAAa,OAAM,aAAY,WAAU,WAAU,mCAAkC,OAAM,QAAO,EAAC;AAA/we,IAAixe,IAAE,EAAC,OAAM,QAAO,QAAO,OAAM,YAAW,mGAAkG,WAAU,mCAAkC,UAAS,YAAW,cAAa,MAAK;AAA7+e,IAA++e,IAAE,EAAC,OAAM,oBAAmB,QAAO,QAAO,UAAS,YAAW,YAAW,MAAK;AAA7jf,IAA+jf,IAAE,EAAC,OAAM,QAAO,cAAa,QAAO,QAAO,QAAO,WAAU,cAAa,WAAU,6BAA4B,QAAO,kBAAiB,UAAS,YAAW,WAAU,yBAAwB,QAAO,WAAU,MAAK,qCAAoC;AAAtzf,IAAwzf,IAAE,SAASJ,IAAE;AAAC,SAAOA,KAAE,IAAE,IAAEA,KAAE,IAAE,IAAEA;AAAC;AAA11f,IAA41f,IAAE,SAASA,IAAE;AAAC,MAAIK,KAAEL,GAAE,QAAOM,SAAE,aAAAL,UAAE,KAAE,GAAEM,KAAED,GAAE,CAAC,GAAEE,KAAEF,GAAE,CAAC,GAAEG,SAAE,aAAAP,QAAE,IAAI,GAAEQ,SAAE,aAAAP,aAAG,SAASH,IAAE;AAAC,QAAG,CAACS,GAAE,QAAQ,QAAM,EAAC,MAAK,GAAE,KAAI,EAAC;AAAE,QAAIR,KAAEQ,GAAE,QAAQ,sBAAsB,GAAEP,KAAED,GAAE,OAAME,KAAEF,GAAE,MAAKG,KAAEH,GAAE,KAAII,KAAEJ,GAAE,QAAOK,KAAEN,GAAE,SAAO,YAAU,OAAOA,GAAE,QAAMA,KAAEA,GAAE,QAAQ,CAAC,GAAEO,KAAED,GAAE,OAAME,KAAEF,GAAE;AAAM,WAAM,EAAC,MAAK,GAAGC,MAAGJ,KAAE,OAAO,gBAAcD,EAAC,GAAE,KAAI,GAAGM,MAAGJ,KAAE,OAAO,gBAAcC,EAAC,EAAC;AAAA,EAAC,GAAG,CAAC,CAAC,GAAEM,SAAE,aAAAR,aAAG,SAASH,IAAE;AAAC,IAAAA,GAAE,eAAe,GAAES,GAAE,WAASJ,GAAEK,GAAEV,EAAC,CAAC;AAAA,EAAC,GAAG,CAACK,IAAEK,EAAC,CAAC,GAAEE,SAAE,aAAAT,aAAG,SAASH,IAAE;AAAC,IAAAK,GAAEK,GAAEV,EAAC,CAAC,GAAEQ,GAAE,IAAE;AAAA,EAAC,GAAG,CAACH,IAAEK,EAAC,CAAC,GAAEG,SAAE,aAAAV,aAAG,WAAU;AAAC,WAAOK,GAAE,KAAE;AAAA,EAAC,GAAG,CAAC,CAAC,GAAEM,SAAE,aAAAX,aAAG,SAASH,IAAE;AAAC,QAAIC,KAAED,KAAE,SAAS,mBAAiB,SAAS;AAAoB,IAAAC,GAAE,aAAYU,EAAC,GAAEV,GAAE,aAAYU,EAAC,GAAEV,GAAE,WAAUY,EAAC,GAAEZ,GAAE,YAAWY,EAAC;AAAA,EAAC,GAAG,CAACF,IAAEE,EAAC,CAAC;AAAE,aAAO,aAAAT,iBAAG,WAAU;AAAC,WAAOU,GAAEP,EAAC,GAAE,WAAU;AAAC,MAAAA,MAAGO,GAAE,KAAE;AAAA,IAAC;AAAA,EAAC,GAAG,CAACP,IAAEO,EAAC,CAAC,GAAE,EAAC,KAAIL,IAAE,aAAYG,GAAC;AAAC;AAAljhB,IAAojhB,IAAE,aAAAZ,QAAE,KAAM,SAASC,IAAE;AAAC,MAAIC,KAAED,GAAE,KAAIG,KAAEH,GAAE,UAASI,SAAE,aAAAF,aAAG,SAASH,IAAE;AAAC,QAAIC,KAAED,GAAE;AAAK,WAAOI,MAAGA,GAAE,EAAC,GAAE,MAAIH,IAAE,GAAEC,GAAE,GAAE,GAAEA,GAAE,GAAE,GAAEA,GAAE,EAAC,CAAC;AAAA,EAAC,GAAG,CAACE,EAAC,CAAC,GAAEE,KAAE,EAAE,EAAC,QAAOD,GAAC,CAAC,GAAEE,KAAED,GAAE,KAAIE,KAAEF,GAAE;AAAY,SAAO,aAAAN,QAAE,cAAc,OAAM,EAAC,OAAM,GAAE,KAAIO,IAAE,cAAaC,IAAE,aAAYA,GAAC,GAAE,aAAAR,QAAE,cAAc,OAAM,EAAC,OAAM,EAAC,GAAE,aAAAA,QAAE,cAAc,OAAM,EAAC,OAAM,EAAC,CAAC,CAAC,CAAC;AAAC,CAAE;AAAv2hB,IAAy2hB,IAAE,EAAC,QAAO,WAAU,OAAM,QAAO,QAAO,OAAM,WAAU,mCAAkC,UAAS,YAAW,QAAO,GAAE,cAAa,OAAM,YAAW,8BAA6B;AAA3hiB,IAA6hiB,IAAE,EAAC,UAAS,YAAW,KAAI,GAAE,MAAK,GAAE,OAAM,GAAE,QAAO,GAAE,YAAW,6JAA4J,cAAa,MAAK;AAA3wiB,IAA6wiB,IAAE,EAAC,OAAM,oBAAmB,QAAO,QAAO,UAAS,YAAW,YAAW,MAAK;AAA31iB,IAA61iB,IAAE,EAAC,OAAM,QAAO,cAAa,QAAO,QAAO,QAAO,WAAU,6BAA4B,QAAO,kBAAiB,UAAS,YAAW,WAAU,yBAAwB,QAAO,WAAU,MAAK,wCAAuC,WAAU,aAAY;AAAtljB,IAAwljB,IAAE,EAAC,UAAS,YAAW,KAAI,OAAM,OAAM,OAAM,QAAO,OAAM,MAAK,OAAM,iBAAgB,6LAA4L,oBAAmB,eAAc,QAAO,IAAG,cAAa,qCAAoC;AAAr8jB,IAAu8jB,IAAE,aAAAA,QAAE,KAAM,SAASC,IAAE;AAAC,MAAIC,KAAED,GAAE,UAASG,SAAE,aAAAD,aAAG,SAASH,IAAE;AAAC,QAAIC,KAAED,GAAE;AAAK,WAAOE,MAAGA,GAAE,WAAWD,GAAE,QAAQ,CAAC,CAAC,CAAC;AAAA,EAAC,GAAG,CAACC,EAAC,CAAC,GAAEG,KAAE,EAAE,EAAC,QAAOD,GAAC,CAAC,GAAEE,KAAED,GAAE,KAAIE,KAAEF,GAAE;AAAY,SAAO,aAAAL,QAAE,cAAc,OAAM,EAAC,OAAM,GAAE,KAAIM,IAAE,cAAaC,IAAE,aAAYA,GAAC,GAAE,aAAAP,QAAE,cAAc,OAAM,EAAC,OAAM,EAAC,CAAC,GAAE,aAAAA,QAAE,cAAc,OAAM,EAAC,OAAM,EAAC,CAAC,GAAE,aAAAA,QAAE,cAAc,OAAM,EAAC,OAAM,EAAC,GAAE,aAAAA,QAAE,cAAc,OAAM,EAAC,OAAM,EAAC,CAAC,CAAC,CAAC;AAAC,CAAE;AAAjzkB,IAAmzkB,IAAE,EAAC,SAAQ,UAAS,WAAU,qCAAoC,SAAQ,QAAO,qBAAoB,mBAAkB,SAAQ,SAAQ;AAA17kB,IAA47kB,IAAE,EAAC,QAAO,QAAO,QAAO,KAAI,SAAQ,QAAO,YAAW,QAAO,QAAO,WAAU,SAAQ,KAAI,cAAa,4BAA2B,QAAO,qCAAoC,UAAS,UAAS,UAAS,YAAW,YAAW,QAAO,eAAc,SAAQ;AAAvrlB,IAAyrlB,IAAE,EAAC,QAAO,QAAO,QAAO,KAAI,SAAQ,KAAI,SAAQ,QAAO,YAAW,QAAO,SAAQ,QAAO,YAAW,UAAS,gBAAe,UAAS,QAAO,WAAU,QAAO,QAAO,cAAa,4BAA2B,eAAc,SAAQ;AAA15lB,IAA45lB,IAAE,EAAC,QAAO,OAAM,OAAM,OAAM,MAAK,mBAAkB;AAA/8lB,IAAi9lB,IAAE,SAASA,IAAE;AAAC,SAAM,EAAC,UAAS,YAAW,YAAW,QAAQ,OAAOA,GAAE,GAAE,IAAI,EAAE,OAAOA,GAAE,GAAE,IAAI,EAAE,OAAOA,GAAE,GAAE,IAAI,EAAE,OAAOA,GAAE,GAAE,GAAG,GAAE,KAAI,OAAM,OAAM,OAAM,QAAO,OAAM,MAAK,OAAM,QAAO,EAAC;AAAC;AAAvomB,IAAyomB,IAAE,EAAC,UAAS,YAAW,KAAI,OAAM,OAAM,OAAM,QAAO,OAAM,MAAK,OAAM,iBAAgB,6LAA4L,oBAAmB,eAAc,QAAO,EAAC;AAAn8mB,IAAq8mB,IAAE,aAAAA,QAAE,KAAM,SAASC,IAAE;AAAC,MAAIC,KAAED,GAAE,QAAOE,KAAEF,GAAE,SAAQG,KAAEH,GAAE;AAAM,SAAO,aAAAD,QAAE,cAAc,OAAM,EAAC,OAAM,EAAC,GAAEE,GAAE,IAAK,SAASD,IAAEC,IAAE;AAAC,QAAIE,KAAE,EAAEH,EAAC;AAAE,QAAG,CAACG,GAAE,QAAQ,EAAE,OAAM,MAAM,GAAG,OAAOH,IAAE,wBAAwB,CAAC;AAAE,QAAII,KAAED,GAAE,MAAM;AAAE,WAAO,aAAAJ,QAAE,cAAc,UAAS,EAAC,KAAIE,IAAE,OAAM,GAAE,SAAQ,WAAU;AAAC,aAAOC,GAAEE,EAAC;AAAA,IAAC,GAAE,MAAK,SAAQ,GAAE,aAAAL,QAAE,cAAc,OAAM,EAAC,OAAM,EAAEK,EAAC,EAAC,CAAC,GAAE,aAAAL,QAAE,cAAc,OAAM,EAAC,OAAM,EAAC,CAAC,CAAC;AAAA,EAAC,CAAE,GAAEI,MAAG,aAAAJ,QAAE,cAAc,UAAS,EAAC,OAAM,GAAE,SAAQI,IAAE,MAAK,SAAQ,GAAE,aAAAJ,QAAE,cAAc,OAAM,EAAC,OAAM,8BAA6B,OAAM,MAAK,QAAO,MAAK,SAAQ,aAAY,OAAM,EAAC,GAAE,aAAAA,QAAE,cAAc,QAAO,EAAC,GAAE,iDAAgD,CAAC,CAAC,CAAC,CAAC;AAAC,CAAE;AAA9joB,IAAgkoB,IAAE,EAAC,UAAS,YAAW,KAAI,GAAE,MAAK,GAAE,QAAO,GAAE,OAAM,EAAC;AAApnoB,IAAsnoB,IAAE,EAAC,OAAM,uBAAsB,QAAO,SAAQ,QAAO,UAAS,UAAS,YAAW,YAAW,iCAAgC,cAAa,4BAA2B,QAAO,qCAAoC,UAAS,SAAQ;AAAv1oB,IAAy1oB,IAAE,EAAC,UAAS,YAAW,QAAO,QAAO,KAAI,qCAAoC,MAAK,qCAAoC;AAA/8oB,IAAi9oB,IAAE,EAAC,OAAM,QAAO,cAAa,QAAO,QAAO,QAAO,WAAU,cAAa,WAAU,6BAA4B,QAAO,iBAAgB;AAAvlpB,IAAylpB,IAAE,aAAAA,QAAE,KAAM,SAASC,IAAE;AAAC,MAAIC,KAAED,GAAE,KAAIG,KAAEH,GAAE,UAASI,SAAE,aAAAF,aAAG,SAASH,IAAE;AAAC,QAAIC,KAAED,GAAE,MAAKG,KAAEH,GAAE;AAAI,WAAOI,MAAGA,GAAE,EAAE,EAAE,CAAC,GAAEF,EAAC,GAAE,EAAC,GAAED,IAAE,GAAE,IAAEE,GAAC,CAAC,CAAC;AAAA,EAAC,GAAG,CAACC,EAAC,CAAC,GAAEG,KAAE,EAAE,EAAC,QAAOF,GAAC,CAAC,GAAEG,KAAED,GAAE,KAAIE,KAAEF,GAAE;AAAY,SAAO,aAAAP,QAAE,cAAc,OAAM,EAAC,OAAM,GAAE,KAAIQ,IAAE,cAAaC,IAAE,aAAYA,GAAC,GAAE,aAAAT,QAAE,cAAc,SAAQ,MAAK,gZAAgZ,GAAE,aAAAA,QAAE,cAAc,OAAM,EAAC,OAAM,GAAE,WAAU,mBAAkB,GAAE,aAAAA,QAAE,cAAc,OAAM,EAAC,OAAM,GAAE,WAAU,mBAAkB,CAAC,GAAE,aAAAA,QAAE,cAAc,OAAM,EAAC,OAAM,EAAC,GAAE,aAAAA,QAAE,cAAc,OAAM,EAAC,OAAM,EAAC,CAAC,CAAC,CAAC,CAAC;AAAC,CAAE;AAA17qB,IAA47qB,IAAE,aAAAA,QAAE,KAAM,SAASE,IAAE;AAAC,MAAIE,KAAEF,GAAE,QAAOI,KAAEJ,GAAE,SAAQM,KAAEN,GAAE,cAAaO,SAAE,aAAAR,UAAE,CAAC,CAAC,GAAES,KAAED,GAAE,CAAC,GAAEE,KAAEF,GAAE,CAAC;AAAE,mBAAAJ,WAAG,WAAU;AAAC,QAAIL,KAAE,KAAK,MAAM,OAAO,aAAa,QAAQ,aAAa,KAAG,IAAI;AAAE,UAAM,QAAQA,EAAC,KAAGW,GAAEX,EAAC;AAAA,EAAC,GAAG,CAAC,CAAC;AAAE,MAAIY,SAAE,aAAAT,aAAG,WAAU;AAAC,QAAIH,KAAE,EAAE,EAAE,CAAC,GAAEU,IAAE,IAAE,GAAE,CAACF,EAAC,GAAE,KAAE;AAAE,WAAO,aAAa,QAAQ,eAAc,KAAK,UAAUR,EAAC,CAAC,GAAEW,GAAEX,EAAC;AAAA,EAAC,GAAG,CAACI,IAAEI,EAAC,CAAC;AAAE,SAAO,aAAAR,QAAE,cAAc,GAAE,EAAC,SAAQM,IAAE,QAAO,EAAE,EAAE,CAAC,GAAEF,IAAE,IAAE,GAAEM,IAAE,IAAE,GAAE,OAAME,GAAC,CAAC;AAAC,CAAE;AAAr0rB,IAAu0rB,IAAE,EAAC,YAAW,yBAAwB,WAAU,yBAAwB,cAAa,4BAA2B,OAAM,oBAAmB,UAAS,SAAQ,QAAO,oCAAmC;AAA3gsB,IAA6gsB,IAAE,EAAC,UAAS,YAAW,OAAM,QAAO,QAAO,QAAO,cAAa,4BAA2B,UAAS,UAAS,WAAU,uCAAsC,iBAAgB,6LAA4L,oBAAmB,cAAa;AAAr5sB,IAAu5sB,IAAE,EAAC,YAAW,+BAA8B,cAAa,2BAA0B;AAA1+sB,IAA4+sB,IAAE,EAAC,UAAS,YAAW,KAAI,OAAM,OAAM,OAAM,QAAO,OAAM,MAAK,OAAM,YAAW,mBAAkB,QAAO,GAAE,QAAO,qCAAoC,cAAa,2BAA0B;AAAzqtB,IAA2qtB,IAAE,EAAC,QAAO,iBAAgB,OAAM,uBAAsB,SAAQ,QAAO,YAAW,UAAS,gBAAe,UAAS,UAAS,OAAM;AAA3ytB,IAA6ytB,IAAE,EAAC,UAAS,GAAE,YAAW,SAAQ;AAA90tB,IAAg1tB,IAAE,EAAC,QAAO,mBAAkB,SAAQ,QAAO,YAAW,UAAS,gBAAe,gBAAe;AAA76tB,IAA+6tB,IAAE,EAAC,SAAQ,QAAO,YAAW,UAAS,OAAM,oBAAmB,QAAO,qCAAoC,cAAa,4BAA2B,QAAO,UAAS,YAAW,8BAA6B;AAAznuB,IAA2nuB,IAAE,EAAC,OAAM,QAAO,YAAW,QAAO,OAAM,oBAAmB,QAAO,QAAO,cAAa,4BAA2B,SAAQ,OAAM,UAAS,QAAO,QAAO,KAAI,eAAc,aAAY,WAAU,aAAY;AAAr0uB,IAAu0uB,IAAE,EAAC,aAAY,OAAM,UAAS,QAAO,OAAM,yBAAwB;AAA14uB,IAA44uB,IAAE,EAAC,SAAQ,QAAO,eAAc,UAAS,YAAW,UAAS,YAAW,cAAa,UAAS,QAAO,OAAM,mBAAkB;AAAzgvB,IAA2gvB,IAAE,aAAAZ,QAAE,KAAM,SAASC,IAAE;AAAC,MAAIC,KAAED,GAAE,MAAKE,KAAEF,GAAE,OAAMG,KAAEH,GAAE,MAAKI,KAAE,WAASD,KAAE,SAAOA,IAAEG,KAAEN,GAAE,OAAMO,KAAEP,GAAE,QAAOQ,KAAER,GAAE,KAAIS,KAAET,GAAE,KAAIU,KAAEV,GAAE,MAAKW,KAAEX,GAAE,WAAUY,KAAEZ,GAAE,MAAKa,KAAE,WAASD,KAAE,UAAQA,IAAEE,KAAEd,GAAE,UAAS0B,KAAE1B,GAAE,QAAO2B,KAAE,YAAUd,KAAE,SAAO;AAAO,SAAO,aAAAd,QAAE,cAAc,SAAQ,EAAC,OAAM,EAAC,GAAE,aAAAA,QAAE,cAAc,OAAM,EAAC,OAAM,EAAC,GAAEQ,MAAG,aAAAR,QAAE,cAAc,QAAO,EAAC,OAAM,EAAC,GAAEQ,EAAC,GAAE,aAAAR,QAAE,cAAc,SAAQ,EAAC,cAAa,OAAM,MAAKK,IAAE,MAAKH,IAAE,OAAMC,IAAE,UAAS,SAASH,IAAE;AAAC,WAAOe,GAAEf,GAAE,OAAO,KAAK;AAAA,EAAC,GAAE,QAAO,SAASA,IAAE;AAAC,QAAG2B,GAAE,QAAOA,GAAE3B,GAAE,OAAO,KAAK;AAAE,IAAAe,GAAEf,GAAE,OAAO,KAAK;AAAA,EAAC,GAAE,OAAM,EAAE,EAAE,CAAC,GAAE,CAAC,GAAE,EAAC,OAAM4B,GAAC,CAAC,GAAE,YAAW,OAAG,WAAUhB,IAAE,KAAIH,IAAE,KAAIC,IAAE,MAAKC,GAAC,CAAC,CAAC,GAAE,aAAAX,QAAE,cAAc,QAAO,MAAKO,EAAC,CAAC;AAAC,CAAE;AAAxnwB,IAA0nwB,IAAE,aAAAP,QAAE,KAAM,SAASE,IAAE;AAAC,MAAIE,KAAEF,GAAE,OAAMK,KAAEL,GAAE,OAAMM,KAAEN,GAAE,SAAQW,KAAEX,GAAE,UAASY,KAAEZ,GAAE,WAAUa,KAAEb,GAAE,YAAWyB,KAAEzB,GAAE,WAAU0B,KAAE1B,GAAE,cAAa4B,SAAE,aAAA7B,UAAE,EAAEM,EAAC,CAAC,GAAEwB,KAAED,GAAE,CAAC,GAAEE,KAAEF,GAAE,CAAC;AAAE,mBAAAzB,WAAG,WAAU;AAAC,IAAA2B,GAAE,EAAEzB,EAAC,CAAC;AAAA,EAAC,GAAG,CAACA,EAAC,CAAC;AAAE,MAAI0B,SAAE,aAAA9B,aAAG,SAASH,IAAE;AAAC,QAAIC,KAAE,EAAED,EAAC;AAAE,IAAAgC,GAAE/B,EAAC,GAAEY,MAAGA,GAAEZ,EAAC;AAAA,EAAC,GAAG,CAAC8B,EAAC,CAAC,GAAEG,SAAE,aAAA/B,aAAG,SAASH,IAAE;AAAC,WAAOiC,GAAE,EAAE,EAAE,CAAC,GAAEF,GAAE,GAAG,GAAE,EAAC,GAAE/B,GAAC,CAAC,CAAC;AAAA,EAAC,GAAG,CAAC+B,EAAC,CAAC,GAAEK,KAAEL,GAAE,KAAIM,KAAEN,GAAE,KAAIf,KAAEe,GAAE,KAAIO,KAAEP,GAAE,KAAIR,KAAEQ,GAAE,OAAMV,KAAE,EAAC,qBAAoB,QAAMjB,KAAE,SAAOA,GAAE,eAAa,EAAE,MAAM,YAAW,2BAA0B,QAAMA,KAAE,SAAOA,GAAE,oBAAkB,EAAE,MAAM,iBAAgB,gBAAe,QAAMA,KAAE,SAAOA,GAAE,UAAQ,EAAE,MAAM,OAAM,uBAAsB,QAAMA,KAAE,SAAOA,GAAE,gBAAc,EAAE,MAAM,aAAY,wBAAuB,QAAMA,KAAE,SAAOA,GAAE,iBAAe,EAAE,MAAM,cAAa,qBAAoB,QAAMA,KAAE,SAAOA,GAAE,cAAY,EAAE,MAAM,WAAU,gBAAe,QAAMA,KAAE,SAAOA,GAAE,UAAQ,EAAE,MAAM,MAAK,GAAEc,KAAE,EAAC,aAAYmB,GAAE,GAAE,aAAYD,GAAE,GAAE,eAAcA,GAAE,GAAE,cAAaA,GAAE,GAAE,aAAYE,IAAE,eAAcf,IAAE,cAAa,QAAQ,OAAOa,GAAE,GAAE,IAAI,EAAE,OAAOA,GAAE,GAAE,IAAI,EAAE,OAAOA,GAAE,GAAE,IAAI,EAAE,OAAOb,IAAE,GAAG,GAAE,qBAAoB,GAAG,OAAO,MAAIc,GAAE,IAAE,KAAI,GAAG,GAAE,uBAAsB,GAAG,OAAO,MAAId,IAAE,GAAG,GAAE,gCAA+B,QAAQ,OAAO,OAAKP,GAAE,IAAE,KAAI,UAAU,GAAE,iCAAgC,QAAQ,OAAO,MAAIA,GAAE,GAAE,UAAU,EAAC,GAAEQ,KAAE,SAASxB,IAAEC,IAAE;AAAC,QAAIC;AAAE,QAAG,EAAE,OAAKD,MAAGA,GAAE,SAAO,IAAG;AAAC,UAAIE,KAAE,QAAMH,KAAE,SAASC,EAAC,IAAE,MAAI,SAASA,EAAC;AAAE,MAAAgC,GAAE,EAAE,EAAE,CAAC,GAAEG,EAAC,KAAIlC,KAAE,CAAC,GAAGF,EAAC,IAAEG,IAAED,GAAE,CAAC;AAAA,IAAC;AAAA,EAAC;AAAE,SAAO,aAAAF,QAAE,cAAc,OAAM,EAAC,OAAM,EAAE,EAAE,EAAE,CAAC,GAAEqB,EAAC,GAAE,CAAC,GAAEH,EAAC,GAAE,WAAUS,GAAC,GAAE,aAAA3B,QAAE,cAAc,GAAE,EAAC,KAAIqC,IAAE,UAASJ,GAAC,CAAC,GAAE,aAAAjC,QAAE,cAAc,OAAM,EAAC,OAAM,EAAC,GAAE,aAAAA,QAAE,cAAc,OAAM,EAAC,OAAM,EAAC,GAAE,aAAAA,QAAE,cAAc,OAAM,EAAC,OAAM,EAAC,GAAE,aAAAA,QAAE,cAAc,OAAM,EAAC,OAAM,EAAC,CAAC,CAAC,CAAC,GAAE,aAAAA,QAAE,cAAc,OAAM,EAAC,OAAM,EAAC,GAAE,aAAAA,QAAE,cAAc,GAAE,EAAC,KAAIqC,IAAE,UAASJ,GAAC,CAAC,GAAE,CAACnB,MAAG,aAAAd,QAAE,cAAc,GAAE,EAAC,UAASkC,GAAC,CAAC,CAAC,CAAC,GAAE,CAACnB,MAAG,aAAAf,QAAE,cAAc,OAAM,EAAC,OAAM,EAAC,GAAE,aAAAA,QAAE,cAAc,GAAE,EAAC,MAAK,QAAO,MAAK,OAAM,OAAM,OAAM,MAAK,SAAQ,QAAO,KAAI,UAAS,SAASA,IAAE;AAAC,QAAG,iBAAiB,KAAKA,EAAC,GAAE;AAAC,UAAIC,KAAE,EAAE,IAAI,OAAOD,EAAC,CAAC;AAAE,MAAAgC,GAAE,EAAC,KAAI,IAAI,OAAOhC,EAAC,GAAE,KAAIC,GAAE,MAAM,GAAE,KAAIA,GAAE,MAAM,GAAE,KAAIA,GAAE,MAAM,GAAE,OAAMA,GAAE,SAAS,EAAC,CAAC;AAAA,IAAC;AAAA,EAAC,GAAE,QAAOgC,IAAE,WAAU,GAAE,OAAMK,GAAE,QAAQ,KAAI,EAAE,EAAC,CAAC,GAAE,aAAAtC,QAAE,cAAc,OAAM,EAAC,OAAM,EAAC,SAAQ,OAAM,EAAC,GAAE,aAAAA,QAAE,cAAc,GAAE,EAAC,OAAMoC,GAAE,GAAE,OAAM,KAAI,MAAK,OAAM,MAAK,UAAS,KAAI,GAAE,KAAI,KAAI,MAAK,GAAE,UAAS,SAASpC,IAAE;AAAC,WAAOwB,GAAE,KAAIxB,EAAC;AAAA,EAAC,EAAC,CAAC,GAAE,aAAAA,QAAE,cAAc,GAAE,EAAC,OAAMoC,GAAE,GAAE,OAAM,KAAI,MAAK,SAAQ,MAAK,UAAS,KAAI,GAAE,KAAI,KAAI,MAAK,GAAE,UAAS,SAASpC,IAAE;AAAC,WAAOwB,GAAE,KAAIxB,EAAC;AAAA,EAAC,EAAC,CAAC,GAAE,aAAAA,QAAE,cAAc,GAAE,EAAC,OAAMoC,GAAE,GAAE,OAAM,KAAI,MAAK,QAAO,MAAK,UAAS,KAAI,GAAE,KAAI,KAAI,MAAK,GAAE,UAAS,SAASpC,IAAE;AAAC,WAAOwB,GAAE,KAAIxB,EAAC;AAAA,EAAC,EAAC,CAAC,GAAE,CAACc,MAAG,aAAAd,QAAE,cAAc,GAAE,EAAC,OAAM,MAAIoC,GAAE,GAAE,OAAM,SAAQ,MAAK,SAAQ,MAAK,UAAS,KAAI,GAAE,KAAI,KAAI,MAAK,GAAE,UAAS,SAASpC,IAAE;AAAC,WAAOwB,GAAE,KAAIxB,EAAC;AAAA,EAAC,EAAC,CAAC,CAAC,CAAC,GAAEQ,MAAG,aAAAR,QAAE,cAAc,GAAE,EAAC,QAAOQ,IAAE,SAAQyB,IAAE,cAAaF,GAAE,IAAG,CAAC,GAAEH,MAAG,aAAA5B,QAAE,cAAc,GAAE,EAAC,QAAO,EAAE+B,IAAEH,EAAC,GAAE,SAAQK,GAAC,CAAC,CAAC;AAAC,CAAE;", "names": ["e", "r", "t", "n", "a", "o", "e", "r", "t", "n", "a", "o", "i", "l", "s", "c", "u", "f", "h", "d", "g", "p", "R", "P", "B", "O", "j", "F", "N", "H", "U", "z", "I", "b", "m", "v", "x", "A", "y", "_", "w", "k", "E", "S", "C", "M", "L", "T"]}