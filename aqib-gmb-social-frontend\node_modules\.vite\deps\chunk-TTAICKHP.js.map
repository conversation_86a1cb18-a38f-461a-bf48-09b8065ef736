{"version": 3, "sources": ["../../@mui/x-internals/esm/warning/warning.js"], "sourcesContent": ["const warnedOnceCache = new Set();\n\n// TODO move to @base_ui/internals. Base UI, etc. need this helper.\nexport function warnOnce(message, gravity = 'warning') {\n  if (process.env.NODE_ENV === 'production') {\n    return;\n  }\n  const cleanMessage = Array.isArray(message) ? message.join('\\n') : message;\n  if (!warnedOnceCache.has(cleanMessage)) {\n    warnedOnceCache.add(cleanMessage);\n    if (gravity === 'error') {\n      console.error(cleanMessage);\n    } else {\n      console.warn(cleanMessage);\n    }\n  }\n}\nexport function clearWarningsCache() {\n  warnedOnceCache.clear();\n}"], "mappings": ";AAAA,IAAM,kBAAkB,oBAAI,IAAI;AAGzB,SAAS,SAAS,SAAS,UAAU,WAAW;AACrD,MAAI,OAAuC;AACzC;AAAA,EACF;AACA,QAAM,eAAe,MAAM,QAAQ,OAAO,IAAI,QAAQ,KAAK,IAAI,IAAI;AACnE,MAAI,CAAC,gBAAgB,IAAI,YAAY,GAAG;AACtC,oBAAgB,IAAI,YAAY;AAChC,QAAI,YAAY,SAAS;AACvB,cAAQ,MAAM,YAAY;AAAA,IAC5B,OAAO;AACL,cAAQ,KAAK,YAAY;AAAA,IAC3B;AAAA,EACF;AACF;", "names": []}