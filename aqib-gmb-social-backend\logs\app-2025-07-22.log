{"timestamp":"2025-07-22T13:15:15.719Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"12424462...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-22T13:15:16.060Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-22T13:15:32.226Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-07-22T07:45:36.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-22T13:15:43.805Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"4c188e2c-61f9-4914-8618-a9fd0ef71389","controller":"auth","action":"login","email":"<EMAIL>"}
{"timestamp":"2025-07-22T13:15:43.807Z","level":"INFO","message":"Login attempt","environment":"DEVELOPMENT","requestId":"4c188e2c-61f9-4914-8618-a9fd0ef71389","email":"<EMAIL>","ip":"::1"}
{"timestamp":"2025-07-22T13:15:43.832Z","level":"WARN","message":"Login failed - invalid credentials","environment":"DEVELOPMENT","requestId":"4c188e2c-61f9-4914-8618-a9fd0ef71389","email":"<EMAIL>","reason":"User not found."}
{"timestamp":"2025-07-22T13:16:53.832Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"c28c76fa-bd5b-478b-b21f-7ce4738d5457","controller":"auth","action":"login","email":"<EMAIL>"}
{"timestamp":"2025-07-22T13:16:53.834Z","level":"INFO","message":"Login attempt","environment":"DEVELOPMENT","requestId":"c28c76fa-bd5b-478b-b21f-7ce4738d5457","email":"<EMAIL>","ip":"::1"}
{"timestamp":"2025-07-22T13:16:54.353Z","level":"INFO","message":"Login successful","environment":"DEVELOPMENT","requestId":"c28c76fa-bd5b-478b-b21f-7ce4738d5457","userId":132,"email":"<EMAIL>"}
{"timestamp":"2025-07-22T13:16:56.895Z","level":"INFO","message":"Fetching location metrics from database","environment":"DEVELOPMENT","locationId":"6066817250999836539","accountId":"102756707311898130422","startDate":"2024-07-22","endDate":"2025-07-22"}
{"timestamp":"2025-07-22T13:16:57.056Z","level":"INFO","message":"Analytics data found in database","environment":"DEVELOPMENT","locationId":"6066817250999836539","metricsCount":7}
{"timestamp":"2025-07-22T13:16:59.459Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"019115e3-1a73-43c3-8134-6e5c962b0db8","controller":"facebook","action":"getPages"}
{"timestamp":"2025-07-22T13:16:59.464Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"5880c275-3451-447a-a962-b22371aa552d","controller":"linkedin","action":"getPages"}
{"timestamp":"2025-07-22T13:16:59.482Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"fa1f8c0c-5bf4-46bc-bc09-8d86d92ee422","controller":"instagram","action":"getAccounts"}
{"timestamp":"2025-07-22T13:16:59.485Z","level":"INFO","message":"Getting Instagram accounts","environment":"DEVELOPMENT","requestId":"fa1f8c0c-5bf4-46bc-bc09-8d86d92ee422","userId":"132"}
{"timestamp":"2025-07-22T13:16:59.494Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"71a5268e-3068-4146-9e20-3af033bd6d2b","controller":"twitter","action":"getTwitterAccounts","userId":"132"}
{"timestamp":"2025-07-22T13:16:59.509Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","userId":132,"pagesCount":1}
{"timestamp":"2025-07-22T13:16:59.688Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","userId":132,"accountCount":0}
{"timestamp":"2025-07-22T13:16:59.690Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","requestId":"71a5268e-3068-4146-9e20-3af033bd6d2b","userId":"132","accountCount":0}
{"timestamp":"2025-07-22T13:16:59.695Z","level":"INFO","message":"Instagram accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-22T13:16:59.705Z","level":"INFO","message":"LinkedIn accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-22T13:17:07.704Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"0a72cd02-f13b-44e7-aff7-e31c20594bbf","controller":"business","action":"businessList","userId":"132"}
{"timestamp":"2025-07-22T13:17:07.706Z","level":"INFO","message":"Fetching business list","environment":"DEVELOPMENT","requestId":"0a72cd02-f13b-44e7-aff7-e31c20594bbf","userId":"132"}
{"timestamp":"2025-07-22T13:17:07.788Z","level":"INFO","message":"Business list fetched successfully","environment":"DEVELOPMENT","requestId":"0a72cd02-f13b-44e7-aff7-e31c20594bbf","userId":"132","businessCount":1}
{"timestamp":"2025-07-22T13:17:07.998Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"d2ba6a16-a78c-44e4-8a24-d9671454702a","controller":"ManageAssets","action":"getAssets"}
{"timestamp":"2025-07-22T13:17:08.188Z","level":"INFO","message":"Asset thumbnail debug","environment":"DEVELOPMENT","assetId":6,"fileName":"1728bb1e-ef30-4ab1-82ef-b4c36f68cc94.png","thumbnail_s3_key":"public-thumbnails/62/1689c240-ec2b-49de-8481-cd01ec4b56d9.jpg","thumbnail_s3_url":"https://gmb-social-assets.s3.us-east-1.amazonaws.com/public-thumbnails/62/1689c240-ec2b-49de-8481-cd01ec4b56d9.jpg","thumbnail_file_name":"1689c240-ec2b-49de-8481-cd01ec4b56d9.jpg","thumbnail_size":14357}
{"timestamp":"2025-07-22T13:17:17.938Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"f5ea23b2-ffed-4b97-9355-d48fe18cea8a","controller":"business","action":"businessList","userId":"132"}
{"timestamp":"2025-07-22T13:17:17.940Z","level":"INFO","message":"Fetching business list","environment":"DEVELOPMENT","requestId":"f5ea23b2-ffed-4b97-9355-d48fe18cea8a","userId":"132"}
{"timestamp":"2025-07-22T13:17:18.000Z","level":"INFO","message":"Business list fetched successfully","environment":"DEVELOPMENT","requestId":"f5ea23b2-ffed-4b97-9355-d48fe18cea8a","userId":"132","businessCount":1}
{"timestamp":"2025-07-22T13:17:20.640Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"3b4e058b-33d8-4495-828f-4a3805bb9daa","controller":"accounts","action":"AccountsList"}
