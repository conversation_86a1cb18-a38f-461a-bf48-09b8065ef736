{"version": 3, "sources": ["../../@mui/material/List/List.js", "../../@mui/material/List/listClasses.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport ListContext from \"./ListContext.js\";\nimport { getListUtilityClass } from \"./listClasses.js\";\nimport { jsxs as _jsxs, jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    disablePadding,\n    dense,\n    subheader\n  } = ownerState;\n  const slots = {\n    root: ['root', !disablePadding && 'padding', dense && 'dense', subheader && 'subheader']\n  };\n  return composeClasses(slots, getListUtilityClass, classes);\n};\nconst ListRoot = styled('ul', {\n  name: '<PERSON><PERSON><PERSON><PERSON>',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, !ownerState.disablePadding && styles.padding, ownerState.dense && styles.dense, ownerState.subheader && styles.subheader];\n  }\n})({\n  listStyle: 'none',\n  margin: 0,\n  padding: 0,\n  position: 'relative',\n  variants: [{\n    props: ({\n      ownerState\n    }) => !ownerState.disablePadding,\n    style: {\n      paddingTop: 8,\n      paddingBottom: 8\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.subheader,\n    style: {\n      paddingTop: 0\n    }\n  }]\n});\nconst List = /*#__PURE__*/React.forwardRef(function List(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiList'\n  });\n  const {\n    children,\n    className,\n    component = 'ul',\n    dense = false,\n    disablePadding = false,\n    subheader,\n    ...other\n  } = props;\n  const context = React.useMemo(() => ({\n    dense\n  }), [dense]);\n  const ownerState = {\n    ...props,\n    component,\n    dense,\n    disablePadding\n  };\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(ListContext.Provider, {\n    value: context,\n    children: /*#__PURE__*/_jsxs(ListRoot, {\n      as: component,\n      className: clsx(classes.root, className),\n      ref: ref,\n      ownerState: ownerState,\n      ...other,\n      children: [subheader, children]\n    })\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? List.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * If `true`, compact vertical padding designed for keyboard and mouse input is used for\n   * the list and list items.\n   * The prop is available to descendant components as the `dense` context.\n   * @default false\n   */\n  dense: PropTypes.bool,\n  /**\n   * If `true`, vertical padding is removed from the list.\n   * @default false\n   */\n  disablePadding: PropTypes.bool,\n  /**\n   * The content of the subheader, normally `ListSubheader`.\n   */\n  subheader: PropTypes.node,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default List;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getListUtilityClass(slot) {\n  return generateUtilityClass('MuiList', slot);\n}\nconst listClasses = generateUtilityClasses('MuiList', ['root', 'padding', 'dense', 'subheader']);\nexport default listClasses;"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,YAAuB;AACvB,wBAAsB;;;ACDf,SAAS,oBAAoB,MAAM;AACxC,SAAO,qBAAqB,WAAW,IAAI;AAC7C;AACA,IAAM,cAAc,uBAAuB,WAAW,CAAC,QAAQ,WAAW,SAAS,WAAW,CAAC;AAC/F,IAAO,sBAAQ;;;ADIf,yBAA2C;AAC3C,IAAM,oBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,QAAQ,CAAC,kBAAkB,WAAW,SAAS,SAAS,aAAa,WAAW;AAAA,EACzF;AACA,SAAO,eAAe,OAAO,qBAAqB,OAAO;AAC3D;AACA,IAAM,WAAW,eAAO,MAAM;AAAA,EAC5B,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW;AACpC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,WAAO,CAAC,OAAO,MAAM,CAAC,WAAW,kBAAkB,OAAO,SAAS,WAAW,SAAS,OAAO,OAAO,WAAW,aAAa,OAAO,SAAS;AAAA,EAC/I;AACF,CAAC,EAAE;AAAA,EACD,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU,CAAC;AAAA,IACT,OAAO,CAAC;AAAA,MACN;AAAA,IACF,MAAM,CAAC,WAAW;AAAA,IAClB,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,EACF,GAAG;AAAA,IACD,OAAO,CAAC;AAAA,MACN;AAAA,IACF,MAAM,WAAW;AAAA,IACjB,OAAO;AAAA,MACL,YAAY;AAAA,IACd;AAAA,EACF,CAAC;AACH,CAAC;AACD,IAAM,OAA0B,iBAAW,SAASA,MAAK,SAAS,KAAK;AACrE,QAAM,QAAQ,gBAAgB;AAAA,IAC5B,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAC;AACD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,YAAY;AAAA,IACZ,QAAQ;AAAA,IACR,iBAAiB;AAAA,IACjB;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,UAAgB,cAAQ,OAAO;AAAA,IACnC;AAAA,EACF,IAAI,CAAC,KAAK,CAAC;AACX,QAAM,aAAa;AAAA,IACjB,GAAG;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,UAAU,kBAAkB,UAAU;AAC5C,aAAoB,mBAAAC,KAAK,oBAAY,UAAU;AAAA,IAC7C,OAAO;AAAA,IACP,cAAuB,mBAAAC,MAAM,UAAU;AAAA,MACrC,IAAI;AAAA,MACJ,WAAW,aAAK,QAAQ,MAAM,SAAS;AAAA,MACvC;AAAA,MACA;AAAA,MACA,GAAG;AAAA,MACH,UAAU,CAAC,WAAW,QAAQ;AAAA,IAChC,CAAC;AAAA,EACH,CAAC;AACH,CAAC;AACD,OAAwC,KAAK,YAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ9E,UAAU,kBAAAC,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOrB,OAAO,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKjB,gBAAgB,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAI1B,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIrB,IAAI,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AACxJ,IAAI;AACJ,IAAO,eAAQ;", "names": ["List", "_jsx", "_jsxs", "PropTypes"]}