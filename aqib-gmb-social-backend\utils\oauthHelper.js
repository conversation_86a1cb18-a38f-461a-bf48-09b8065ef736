const { OAuth2Client } = require("google-auth-library");
const keys = require("../config/OAuthKey.json");

/**
 * Utility function to get the correct redirect URI based on environment
 * @returns {string} The appropriate redirect URI for the current environment
 */
const getRedirectUri = () => {
  const environment = process.env.APP_ENV_NAME || "DEVELOPMENT";
  switch (environment.toUpperCase()) {
    case "PRODUCTION":
      return (
        keys.web.redirect_uris.find((uri) =>
          uri.includes("api.mylocobiz.com")
        ) || keys.web.redirect_uris[0]
      );
    case "STAGING":
      return (
        keys.web.redirect_uris.find((uri) =>
          uri.includes("devapi.mylocobiz.com")
        ) || keys.web.redirect_uris[0]
      );
    default:
      return keys.web.redirect_uris[0]; // localhost for development
  }
};

/**
 * Create a properly configured OAuth2Client for the current environment
 * @returns {OAuth2Client} Configured OAuth2Client instance
 */
const createOAuth2Client = () => {
  return new OAuth2Client(
    keys.web.client_id,
    keys.web.client_secret,
    getRedirectUri()
  );
};

module.exports = {
  getRedirectUri,
  createOAuth2Client,
};
